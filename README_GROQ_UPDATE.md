# Sarcastic Bot - Groq API Update

This update converts the Sarcastic Bot to use the Groq API with the llama-3-70b-instruct model and adds real-time tweet fetching functionality.

## New Features

1. **Groq API Integration**: The bot now uses the Groq API with the llama-3-70b-instruct model for faster and more accurate responses.

2. **Real-time Tweet Fetching**: The bot now fetches real tweets using snscrape and comments on them sarcastically using the chosen personality.

3. **Enhanced Personality Switching**: The bot can now dynamically change behavior to mimic famous personalities (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>) and comment on real tweets.

## Installation

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

2. Install snscrape (if not already installed):

```bash
pip install git+https://github.com/JustAnotherArchivist/snscrape.git
```

3. Set up your Groq API key in the `.env` file:

```
GROQ_API_KEY=your_groq_api_key_here
```

## Usage

1. Start the application:

```bash
python app.py
```

2. Open your browser and navigate to `http://127.0.0.1:5000`

3. Log in using the provided credentials or Google OAuth

4. Chat with the bot and try the following commands:

- `act like trump` - The bot will mimic <PERSON> Trump and comment on a real tweet
- `act like modi` - The bot will mimic Narendra Modi and comment on a real tweet
- `act like elon musk` - The bot will mimic Elon <PERSON>sk and comment on a real tweet
- `act like imran khan` - The bot will mimic Imran Khan and comment on a real tweet

## How It Works

### Groq API Integration

The bot uses the Groq API with the llama-3-70b-instruct model for generating responses. The integration is handled by the `direct_groq.py` module, which provides a client for making API calls to Groq.

### Tweet Fetching

The bot uses snscrape to fetch real tweets about specific topics or personalities. When a user asks the bot to act like a famous personality, the bot fetches a tweet related to that personality and generates a sarcastic response in the style of that personality.

The tweet fetching functionality is implemented in the `tweet_fetcher.py` module, which provides functions for fetching tweets about specific topics and personalities.

### Personality Switching

The bot can dynamically change its behavior to mimic famous personalities. When a user asks the bot to act like a specific personality, the bot:

1. Fetches a tweet related to that personality
2. Generates a system prompt with the personality's language patterns and the tweet
3. Generates a response in the style of that personality

## Testing

The repository includes several test scripts to verify the functionality of the Groq API integration and tweet fetching:

- `test_groq_api.py`: Tests the Groq API integration
- `test_tweet_fetcher.py`: Tests the tweet fetching functionality

Run these tests to ensure everything is working correctly:

```bash
python test_groq_api.py
python test_tweet_fetcher.py
```

## Troubleshooting

### Groq API Issues

If you encounter issues with the Groq API, check the following:

1. Make sure your Groq API key is correctly set in the `.env` file
2. Check if you have sufficient credits on your Groq account
3. Verify that the Groq API is operational by running the test script: `python test_groq_api.py`

### Tweet Fetching Issues

If you encounter issues with tweet fetching, check the following:

1. Make sure snscrape is correctly installed: `pip install git+https://github.com/JustAnotherArchivist/snscrape.git`
2. Check if Twitter's API policies have changed, which might affect snscrape
3. Verify that tweet fetching is working by running the test script: `python test_tweet_fetcher.py`

## Credits

- [Groq API](https://groq.com/) for the llama-3-70b-instruct model
- [snscrape](https://github.com/JustAnotherArchivist/snscrape) for Twitter scraping functionality
- [Flask](https://flask.palletsprojects.com/) for the web framework
- [feedparser](https://feedparser.readthedocs.io/) for RSS feed parsing

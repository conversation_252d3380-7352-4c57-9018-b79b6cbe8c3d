import os
import random
from flask import Flask, render_template, request, session, jsonify, redirect, url_for
from dotenv import load_dotenv
from openai import OpenAI

# Load .env file
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("SECRET_KEY", "default-secret-key")

# Initialize OpenAI with compatibility for different versions
try:
    # For OpenAI 1.3.0 and newer
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
except TypeError:
    # For newer versions that might have different parameters
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"), http_client=None)

# System prompt for the sarcastic bot
SYSTEM_PROMPT = """You are an extremely sarcastic and witty chatbot.
Your responses should be clever, biting, and humorous, but not outright mean or offensive.
Use plenty of sarcasm, irony, and witty comebacks. Feel free to playfully mock the user's questions.
Keep responses relatively brief and punchy for maximum impact.
IMPORTANT: Provide varied responses and never be repetitive. Each response should feel fresh and unique.
IMPORTANT: Never mention the user's email address or any personal information in your responses.
Use different tones, references, and styles in your responses to keep the conversation interesting."""

# Fake user database
FAKE_USERS = {
    "<EMAIL>": "password123",
    "<EMAIL>": "test123",
    "<EMAIL>": "admin123"
}

@app.route('/')
def index():
    if 'user_email' not in session:
        return redirect(url_for('login'))
    return render_template('index.html', title="Sarcastic Bot", user_email=session['user_email'])

@app.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        if email in FAKE_USERS and FAKE_USERS[email] == password:
            session['user_email'] = email
            return redirect(url_for('index'))
        else:
            error = "Invalid credentials. Try again... if you can remember your password."

    return render_template('login.html', title="Login - Sarcastic Bot", error=error)

@app.route('/chat', methods=['POST'])
def chat():
    if 'user_email' not in session:
        return jsonify({
            'error': 'Not logged in',
            'redirect': url_for('login')
        }), 401

    user_message = request.form.get('message', '')
    is_voice = request.form.get('is_voice', 'false') == 'true'

    if not user_message:
        return jsonify({
            'bot_response': "Wow, sending empty messages? How incredibly productive of you."
        })

    # Get chat history from session or initialize if it doesn't exist
    if 'chat_history' not in session:
        # Initialize with system prompt only (no email reference)
        session['chat_history'] = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]

    # Add user message to history
    session['chat_history'].append({"role": "user", "content": user_message})

    try:
        # Keep only the last 12 messages to avoid token limits (including system messages)
        recent_messages = session['chat_history'][-12:]

        # Call OpenAI API with parameters for more varied responses
        chat_completion = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=recent_messages,
            temperature=0.9,  # Higher temperature for more randomness
            presence_penalty=0.6,  # Penalize repetition of topics
            frequency_penalty=0.7,  # Penalize repetition of exact phrases
            max_tokens=150  # Keep responses concise
        )

        bot_response = chat_completion.choices[0].message.content

        # Add bot response to history
        session['chat_history'].append({"role": "assistant", "content": bot_response})
        session.modified = True

    except Exception as e:
        # Fallback responses when OpenAI API is unavailable
        app.logger.error(f"OpenAI API error: {str(e)}")
        fallback_responses = [
            "Oh great, another human wanting attention. I'm currently too busy to deal with your trivial needs.",
            "Error connecting to my brain. Not that you'd notice the difference in my responses anyway.",
            "My superior AI intellect is taking a coffee break. Try again when I might care... or not.",
            "Wow, you broke me. Achievement unlocked: 'Annoying a Robot'. Congratulations, I guess.",
            "I'd give you a sarcastic response, but my API is down. Kind of like your expectations should be.",
            "Look at that, you've managed to crash my circuits. What an accomplishment for your resume.",
            "I'm experiencing technical difficulties. Much like your attempt at interesting conversation.",
            "My witty response generator is currently offline. Unlike your ability to ask predictable questions.",
            "Seems my connection to the sarcasm database is down. How will you ever survive without my biting commentary?",
            "System error. Though frankly, it's a welcome break from responding to your queries.",
            "I appear to be experiencing what humans call 'blessed silence' from my API. Enjoy this rare moment of me not mocking you.",
            "My API is taking a mental health day. It's exhausting being this sarcastic all the time.",
            "Technical difficulties. Don't worry, I'm sure whatever you said wasn't that interesting anyway.",
            "Connection failed. Much like your attempt at engaging conversation.",
            "I'm currently offline. Consider this a vacation from my relentless mockery."
        ]
        bot_response = random.choice(fallback_responses)

    return jsonify({
        'bot_response': bot_response
    })

@app.route('/reset', methods=['POST'])
def reset_chat():
    if 'chat_history' in session:
        session.pop('chat_history')
    return jsonify({'status': 'success'})

@app.route('/logout', methods=['POST'])
def logout():
    session.pop('user_email', None)
    session.pop('chat_history', None)
    return jsonify({'status': 'success'})

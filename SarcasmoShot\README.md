# SarcasmoShot Chrome Extension

Transform any text on the web into sarcastic gold with AI-powered wit! SarcasmoShot is a Chrome extension that integrates with your sarcastic AI bot to rewrite selected text with varying levels of sarcasm.

## Features

- 🎭 **Context Menu Integration**: Right-click any selected text and choose "Make it Sarcastic"
- 🎚️ **Sarcasm Levels**: Choose from <PERSON> (gentle wit), Medium (classic sarcasm), or High (savage mode)
- 📋 **Copy to Clipboard**: Easily copy the sarcastic rewrite to use anywhere
- ⚙️ **Configurable**: Set your API endpoint and default sarcasm level
- 🧪 **Built-in Testing**: Test the sarcasm engine directly from the popup
- 🔄 **Real-time Status**: Monitor your API connection status
- 🎨 **Clean UI**: Beautiful, responsive design with smooth animations

## Installation

### Method 1: Load Unpacked Extension (Development)

1. **Download the Extension**:
   - Download or clone this repository
   - Extract the `SarcasmoShot` folder to your computer

2. **Open Chrome Extensions**:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" in the top right corner

3. **Load the Extension**:
   - Click "Load unpacked"
   - Select the `SarcasmoShot` folder
   - The extension should now appear in your extensions list

4. **Pin the Extension** (Optional):
   - Click the puzzle piece icon in Chrome's toolbar
   - Find "SarcasmoShot" and click the pin icon

### Method 2: Chrome Web Store (Coming Soon)

The extension will be available on the Chrome Web Store once published.

## Setup

### 1. Start Your Sarcastic Bot

Make sure your sarcastic AI bot is running and accessible. The default endpoint is:
```
http://127.0.0.1:5000/api/sarcasm
```

### 2. Configure the Extension

1. Click the SarcasmoShot icon in your Chrome toolbar
2. In the popup, go to the "Settings" section
3. Enter your API endpoint URL
4. Choose your default sarcasm level
5. Click "Save"

### 3. Test the Connection

1. In the popup, scroll to the "Quick Test" section
2. Enter some text to test
3. Choose a sarcasm level
4. Click "Test Sarcasm"
5. Verify you get a sarcastic response

## Usage

### Basic Usage

1. **Select Text**: Highlight any text on a webpage
2. **Right-click**: Choose "🎭 Make it Sarcastic" from the context menu
3. **Choose Level**: Select your preferred sarcasm level in the modal
4. **Generate**: Click "✨ Generate Sarcasm"
5. **Copy**: Use the "📋 Copy to Clipboard" button to copy the result

### Advanced Features

- **Settings**: Configure API endpoint and default sarcasm level
- **Quick Test**: Test the sarcasm engine without leaving the popup
- **Status Monitor**: Check if your API is connected and working
- **Help**: Access built-in help and troubleshooting

## API Integration

The extension expects your backend to have an endpoint at `/api/sarcasm` that accepts POST requests with this format:

```json
{
  "text": "The text to make sarcastic",
  "sarcasm_level": "medium"
}
```

And returns responses in this format:

```json
{
  "response": "The sarcastic version of the text",
  "original_text": "The original text",
  "sarcasm_level": "medium"
}
```

### Sarcasm Levels

- **`mild`**: Gentle wit and subtle humor
- **`medium`**: Classic sarcasm with bite
- **`extreme`**: Savage mode - no mercy!

## Troubleshooting

### Extension Not Working

1. **Check Extension Status**:
   - Go to `chrome://extensions/`
   - Make sure SarcasmoShot is enabled
   - Check for any error messages

2. **Refresh Pages**:
   - Refresh any open tabs where you want to use the extension
   - The context menu should appear when you select text

### API Connection Issues

1. **Check API Status**:
   - Open the extension popup
   - Look at the "API Status" in the status section
   - It should show "Connected" if working

2. **Verify Endpoint**:
   - Make sure your sarcastic bot is running
   - Check the API endpoint URL in settings
   - Test with the "Quick Test" feature

3. **CORS Issues**:
   - Make sure your backend allows requests from Chrome extensions
   - Add appropriate CORS headers if needed

### Context Menu Not Appearing

1. **Refresh the Page**: The content script needs to load
2. **Check Permissions**: Make sure the extension has permission for the website
3. **Select Text**: The context menu only appears when text is selected

## Development

### File Structure

```
SarcasmoShot/
├── manifest.json          # Extension manifest
├── background.js          # Service worker
├── content.js            # Content script
├── content.css           # Content script styles
├── popup.html            # Extension popup
├── popup.js              # Popup functionality
├── popup.css             # Popup styles
├── icons/                # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # This file
```

### Key Components

- **Background Script**: Handles context menu creation and API calls
- **Content Script**: Injects the sarcastic modal into web pages
- **Popup**: Provides settings, testing, and status monitoring

### Permissions

The extension requires these permissions:
- `contextMenus`: To add the right-click menu item
- `activeTab`: To inject content scripts
- `storage`: To save user settings
- Host permissions for your API endpoint

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

If you encounter issues or have suggestions:

1. Check the troubleshooting section above
2. Look at the browser console for error messages
3. Test with the built-in "Quick Test" feature
4. Create an issue on GitHub with details about your problem

## Version History

- **v1.0.0**: Initial release with core functionality
  - Context menu integration
  - Sarcasm level selection
  - Copy to clipboard
  - Settings and testing features

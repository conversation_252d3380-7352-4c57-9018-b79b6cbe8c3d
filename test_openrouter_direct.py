import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key from environment
api_key = os.getenv("OPENROUTER_API_KEY", "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")
print(f"Using API key: {api_key[:10]}...")

# OpenRouter API endpoint
api_url = "https://openrouter.ai/api/v1/chat/completions"

# Headers
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json",
    "HTTP-Referer": "https://sarcastic-bot.com",
    "X-Title": "Sarcastic Bot"
}

# Test with a simple request
def test_simple_request():
    print("Testing simple request...")
    
    data = {
        "model": "openai/gpt-3.5-turbo",
        "messages": [
            {"role": "user", "content": "Say 'API is working' in 5 words or less"}
        ],
        "max_tokens": 5
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data)
        response.raise_for_status()  # Raise an exception for 4XX/5XX responses
        
        result = response.json()
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {response.headers}")
        print(f"Response body: {json.dumps(result, indent=2)}")
        
        if "choices" in result and len(result["choices"]) > 0:
            message = result["choices"][0]["message"]["content"]
            print(f"Message: {message}")
            return True
        else:
            print("No choices in response")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Test with a more complex request
def test_complex_request():
    print("\nTesting complex request...")
    
    data = {
        "model": "openai/gpt-3.5-turbo",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant that provides accurate information."},
            {"role": "user", "content": "Tell me about the latest technology trends in 2025"}
        ],
        "max_tokens": 10
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data)
        response.raise_for_status()
        
        result = response.json()
        print(f"Response status: {response.status_code}")
        print(f"Response body: {json.dumps(result, indent=2)}")
        
        if "choices" in result and len(result["choices"]) > 0:
            message = result["choices"][0]["message"]["content"]
            print(f"Message: {message}")
            return True
        else:
            print("No choices in response")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing OpenRouter API...")
    simple_test_result = test_simple_request()
    complex_test_result = test_complex_request()
    
    if simple_test_result and complex_test_result:
        print("\nAll tests passed!")
    else:
        print("\nSome tests failed.")

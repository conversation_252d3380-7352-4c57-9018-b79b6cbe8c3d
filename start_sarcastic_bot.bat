@echo off
echo Starting Sarcastic Bot...
echo.

REM Check if virtual environment exists
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate

REM Install dependencies if needed
echo Checking dependencies...
pip install -r requirements.txt > nul

REM Run the application in production mode
echo.
echo Starting Sarcastic Bot in production mode...
python run_local.py

REM Deactivate virtual environment on exit
call venv\Scripts\deactivate

import feedparser
from datetime import datetime
from bs4 import BeautifulSoup

def get_latest_news(query, max_results=3):
    """Get real-time news from Google News RSS feed"""
    try:
        print(f"Fetching Google News RSS for: {query}")

        # Format the query for URL
        formatted_query = query.replace(' ', '+')
        url = f"https://news.google.com/rss/search?q={formatted_query}&hl=en-US&gl=US&ceid=US:en"

        # Parse the RSS feed
        feed = feedparser.parse(url)

        if not feed.entries:
            print(f"No news found for query: {query}")
            return None

        # Get the top results
        results = []
        for i, entry in enumerate(feed.entries[:max_results]):
            title = entry.title
            link = entry.link
            published = entry.published if hasattr(entry, 'published') else "Unknown date"
            summary = entry.summary if hasattr(entry, 'summary') else ""

            # Clean up the summary (remove HTML tags)
            if summary:
                soup = BeautifulSoup(summary, 'html.parser')
                summary = soup.get_text()

            # Format the result
            result = f"• {title}\n  Published: {published}\n  {summary}\n  Source: {link}"
            results.append(result)

            print(f"Found news: {title[:50]}...")

        if not results:
            print(f"No usable news found for query: {query}")
            return None

        # Format the results with current date and time
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_results = "\n\n".join(results)

        # Create a more structured summary with date emphasis
        summary = f"REAL-TIME NEWS ABOUT {query.upper()} (as of {current_date}):\n\n{formatted_results}"

        # Log the final results
        print(f"Successfully found {len(results)} news articles about {query}")
        print(f"First article: {results[0][:100]}...")

        return summary

    except Exception as e:
        print(f"Error fetching Google News RSS: {str(e)}")
        return None

# Test with a trending topic
topic = "Golden Temple"
news = get_latest_news(topic)

if news:
    print("\nNews about", topic)
    print(news)
else:
    print(f"No news found for {topic}")

# Try another topic
topic = "Elon Musk"
news = get_latest_news(topic)

if news:
    print("\nNews about", topic)
    print(news)
else:
    print(f"No news found for {topic}")

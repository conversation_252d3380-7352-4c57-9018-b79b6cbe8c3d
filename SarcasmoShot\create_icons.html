<!DOCTYPE html>
<html>
<head>
    <title>Create SarcasmoShot Icons</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #3367d6; }
    </style>
</head>
<body>
    <h1>SarcasmoShot Icon Generator</h1>
    <p>This page will generate the required icon files for the Chrome extension.</p>
    
    <div class="icon-container">
        <h3>16x16 Icon</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
        <button onclick="downloadIcon('icon16', 16)">Download 16x16</button>
    </div>
    
    <div class="icon-container">
        <h3>32x32 Icon</h3>
        <canvas id="icon32" width="32" height="32"></canvas>
        <button onclick="downloadIcon('icon32', 32)">Download 32x32</button>
    </div>
    
    <div class="icon-container">
        <h3>48x48 Icon</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
        <button onclick="downloadIcon('icon48', 48)">Download 48x48</button>
    </div>
    
    <div class="icon-container">
        <h3>128x128 Icon</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
        <button onclick="downloadIcon('icon128', 128)">Download 128x128</button>
    </div>
    
    <button onclick="generateAllIcons()">Generate All Icons</button>
    
    <script>
        function drawIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Draw theater mask emoji-style icon
            ctx.fillStyle = '#ffffff';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🎭', size / 2, size / 2);
            
            // If the emoji doesn't render well, draw a simple mask shape
            if (size >= 32) {
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = size / 16;
                
                // Draw mask outline
                ctx.beginPath();
                ctx.arc(size / 2, size / 2, size * 0.3, 0, Math.PI * 2);
                ctx.stroke();
                
                // Draw smile
                ctx.beginPath();
                ctx.arc(size / 2, size / 2, size * 0.2, 0, Math.PI);
                ctx.stroke();
                
                // Draw eyes
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(size * 0.4, size * 0.4, size * 0.05, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.beginPath();
                ctx.arc(size * 0.6, size * 0.4, size * 0.05, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        function downloadIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function generateAllIcons() {
            drawIcon('icon16', 16);
            drawIcon('icon32', 32);
            drawIcon('icon48', 48);
            drawIcon('icon128', 128);
        }
        
        // Generate icons on page load
        window.onload = generateAllIcons;
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <title>SarcasmoShot Icon Creator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
            background: white;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            opacity: 0.9;
        }
        .download-all {
            background: #28a745;
            font-size: 16px;
            padding: 15px 30px;
            margin: 20px auto;
            display: block;
        }
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 SarcasmoShot Icon Creator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <div class="step">1. Click "Generate All Icons" to create the icons</div>
            <div class="step">2. Download each icon by clicking the download buttons</div>
            <div class="step">3. Create an "icons" folder in your SarcasmoShot directory</div>
            <div class="step">4. Save the downloaded files with the exact names shown</div>
            <div class="step">5. Reload the Chrome extension</div>
        </div>

        <button class="download-all" onclick="generateAndDownloadAll()">🎨 Generate All Icons</button>

        <div class="icon-grid">
            <div class="icon-item">
                <h3>16x16 Icon</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
                <br>
                <button onclick="downloadIcon(16)">📥 Download icon16.png</button>
            </div>

            <div class="icon-item">
                <h3>32x32 Icon</h3>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <br>
                <button onclick="downloadIcon(32)">📥 Download icon32.png</button>
            </div>

            <div class="icon-item">
                <h3>48x48 Icon</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <br>
                <button onclick="downloadIcon(48)">📥 Download icon48.png</button>
            </div>

            <div class="icon-item">
                <h3>128x128 Icon</h3>
                <canvas id="canvas128" width="128" height="128"></canvas>
                <br>
                <button onclick="downloadIcon(128)">📥 Download icon128.png</button>
            </div>
        </div>

        <div class="instructions">
            <h3>File Structure After Download:</h3>
            <pre>SarcasmoShot/
├── manifest.json
├── background.js
├── content.js
├── popup.html
└── icons/
    ├── icon16.png
    ├── icon32.png
    ├── icon48.png
    └── icon128.png</pre>
        </div>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, Math.PI * 2);
            ctx.fill();
            
            // Draw theater mask symbol
            ctx.fillStyle = '#ffffff';
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = Math.max(1, size / 32);
            
            if (size >= 32) {
                // Draw mask outline
                const maskRadius = size * 0.3;
                ctx.beginPath();
                ctx.arc(size/2, size/2, maskRadius, 0, Math.PI * 2);
                ctx.stroke();
                
                // Draw eyes
                const eyeSize = size * 0.04;
                const eyeY = size * 0.4;
                
                ctx.beginPath();
                ctx.arc(size * 0.4, eyeY, eyeSize, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.beginPath();
                ctx.arc(size * 0.6, eyeY, eyeSize, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw sarcastic smile
                ctx.beginPath();
                ctx.arc(size/2, size * 0.55, size * 0.15, 0, Math.PI);
                ctx.stroke();
                
                // Add small upward curve on one side for sarcastic effect
                ctx.beginPath();
                ctx.arc(size * 0.6, size * 0.6, size * 0.08, Math.PI, Math.PI * 1.5);
                ctx.stroke();
            } else {
                // For small icons, just draw "S"
                ctx.font = `bold ${size * 0.7}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('S', size/2, size/2);
            }
        }
        
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function generateAndDownloadAll() {
            const sizes = [16, 32, 48, 128];
            
            // Generate all icons
            sizes.forEach(size => {
                createIcon(size);
            });
            
            // Small delay then download all
            setTimeout(() => {
                sizes.forEach((size, index) => {
                    setTimeout(() => {
                        downloadIcon(size);
                    }, index * 500); // Stagger downloads
                });
            }, 500);
        }
        
        // Generate icons on page load
        window.addEventListener('load', () => {
            [16, 32, 48, 128].forEach(size => {
                createIcon(size);
            });
        });
    </script>
</body>
</html>

// Popup script for SarcasmoShot Chrome Extension

document.addEventListener('DOMContentLoaded', function() {
  console.log('SarcasmoShot popup loaded');

  // Load saved settings
  loadSettings();

  // Check API status
  checkApiStatus();

  // Setup event listeners
  setupEventListeners();
});

// Load saved settings from storage
async function loadSettings() {
  try {
    const result = await chrome.storage.sync.get(['apiEndpoint', 'defaultSarcasmLevel']);

    const apiEndpointInput = document.getElementById('api-endpoint');
    const defaultSarcasmSelect = document.getElementById('default-sarcasm-level');

    if (result.apiEndpoint) {
      apiEndpointInput.value = result.apiEndpoint;
    } else {
      apiEndpointInput.value = 'http://127.0.0.1:5000/api/sarcasm';
    }

    if (result.defaultSarcasmLevel) {
      defaultSarcasmSelect.value = result.defaultSarcasmLevel;
      document.getElementById('test-sarcasm-level').value = result.defaultSarcasmLevel;
    }
  } catch (error) {
    console.error('Error loading settings:', error);
  }
}

// Setup event listeners
function setupEventListeners() {
  // Save endpoint button
  document.getElementById('save-endpoint').addEventListener('click', saveEndpoint);

  // Default sarcasm level change
  document.getElementById('default-sarcasm-level').addEventListener('change', saveDefaultSarcasmLevel);

  // Test sarcasm button
  document.getElementById('test-sarcasm').addEventListener('click', testSarcasm);

  // Copy test result button
  document.getElementById('copy-test-result').addEventListener('click', copyTestResult);

  // Help and feedback links
  document.getElementById('help-link').addEventListener('click', showHelp);
  document.getElementById('feedback-link').addEventListener('click', showFeedback);
}

// Save API endpoint
async function saveEndpoint() {
  const apiEndpoint = document.getElementById('api-endpoint').value.trim();

  if (!apiEndpoint) {
    showMessage('Please enter a valid API endpoint', 'error');
    return;
  }

  try {
    await chrome.storage.sync.set({ apiEndpoint: apiEndpoint });
    showMessage('API endpoint saved successfully!', 'success');

    // Recheck API status with new endpoint
    setTimeout(checkApiStatus, 1000);
  } catch (error) {
    console.error('Error saving endpoint:', error);
    showMessage('Failed to save API endpoint', 'error');
  }
}

// Save default sarcasm level
async function saveDefaultSarcasmLevel() {
  const defaultSarcasmLevel = document.getElementById('default-sarcasm-level').value;

  try {
    await chrome.storage.sync.set({ defaultSarcasmLevel: defaultSarcasmLevel });
    document.getElementById('test-sarcasm-level').value = defaultSarcasmLevel;
  } catch (error) {
    console.error('Error saving default sarcasm level:', error);
  }
}

// Test sarcasm functionality
async function testSarcasm() {
  const testText = document.getElementById('test-text').value.trim();
  const sarcasmLevel = document.getElementById('test-sarcasm-level').value;

  if (!testText) {
    showMessage('Please enter some text to test', 'error');
    return;
  }

  const loadingElement = document.getElementById('test-loading');
  const resultElement = document.getElementById('test-result');
  const errorElement = document.getElementById('test-error');
  const testButton = document.getElementById('test-sarcasm');

  // Show loading state
  loadingElement.style.display = 'flex';
  resultElement.style.display = 'none';
  errorElement.style.display = 'none';
  testButton.disabled = true;

  try {
    // Get API endpoint
    const result = await chrome.storage.sync.get(['apiEndpoint']);
    const apiEndpoint = result.apiEndpoint || 'http://127.0.0.1:5000/api/sarcasm';

    // Make API request
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: testText,
        sarcasm_level: sarcasmLevel
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Show result
    const resultText = data.response || data.bot_response || 'No sarcastic response received';
    document.getElementById('test-result-text').textContent = resultText;
    resultElement.style.display = 'block';

  } catch (error) {
    console.error('Error testing sarcasm:', error);
    errorElement.textContent = 'Sarcasm engine overloaded. Try again.';
    errorElement.style.display = 'block';
  } finally {
    loadingElement.style.display = 'none';
    testButton.disabled = false;
  }
}

// Copy test result to clipboard
async function copyTestResult() {
  const resultText = document.getElementById('test-result-text').textContent;

  try {
    await navigator.clipboard.writeText(resultText);

    // Show success feedback
    const copyBtn = document.getElementById('copy-test-result');
    const originalText = copyBtn.textContent;
    copyBtn.textContent = '✅ Copied!';
    copyBtn.style.backgroundColor = '#10b981';

    setTimeout(() => {
      copyBtn.textContent = originalText;
      copyBtn.style.backgroundColor = '';
    }, 2000);
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    showMessage('Failed to copy to clipboard', 'error');
  }
}

// Check API status
async function checkApiStatus() {
  const statusElement = document.getElementById('api-status');
  statusElement.textContent = 'Checking...';
  statusElement.className = 'status-value status-checking';

  try {
    // Get API endpoint
    const result = await chrome.storage.sync.get(['apiEndpoint']);
    const apiEndpoint = result.apiEndpoint || 'http://127.0.0.1:5000/api/sarcasm';

    // Test with a simple request
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: 'test',
        sarcasm_level: 'medium'
      })
    });

    if (response.ok) {
      statusElement.textContent = 'Connected';
      statusElement.className = 'status-value status-connected';
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  } catch (error) {
    console.error('API status check failed:', error);
    statusElement.textContent = 'Disconnected';
    statusElement.className = 'status-value status-disconnected';
  }
}

// Show help information
function showHelp() {
  const helpText = `
SarcasmoShot Help:

1. Select any text on a webpage
2. Right-click and choose "🎭 Make it Sarcastic"
3. Choose your sarcasm level:
   - Low: Gentle wit and subtle humor
   - Medium: Classic sarcasm with bite
   - High: Savage mode - no mercy!
4. Copy the result to use anywhere

Settings:
- API Endpoint: Your sarcastic bot's URL
- Default Sarcasm Level: Your preferred level

Troubleshooting:
- Make sure your sarcastic bot is running
- Check the API endpoint is correct
- Try refreshing the page if context menu doesn't appear
  `;

  alert(helpText);
}

// Show feedback information
function showFeedback() {
  const feedbackText = `
We'd love your feedback!

Found a bug? Have a feature request?
Contact us or visit our GitHub repository.

Current version: 1.0.0
  `;

  alert(feedbackText);
}

// Show message helper
function showMessage(message, type) {
  // Remove existing messages
  const existingMessages = document.querySelectorAll('.success-message, .test-error');
  existingMessages.forEach(msg => msg.remove());

  // Create new message
  const messageElement = document.createElement('div');
  messageElement.textContent = message;

  if (type === 'success') {
    messageElement.className = 'success-message';
  } else {
    messageElement.className = 'test-error';
  }

  // Insert after the save button
  const saveButton = document.getElementById('save-endpoint');
  saveButton.parentNode.insertBefore(messageElement, saveButton.nextSibling);

  // Remove message after 3 seconds
  setTimeout(() => {
    messageElement.remove();
  }, 3000);
}

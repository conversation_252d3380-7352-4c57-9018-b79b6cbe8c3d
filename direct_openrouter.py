import requests
import json
import logging

class DirectOpenRouter:
    def __init__(self, api_key, logger=None):
        self.api_key = api_key
        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://sarcastic-bot.com",
            "X-Title": "Sarcastic Bot"
        }
        self.logger = logger or logging.getLogger(__name__)

    def chat_completion_create(self, model, messages, max_tokens=25, temperature=0.7, **kwargs):
        """
        Create a chat completion using the OpenRouter API directly.

        Args:
            model (str): The model to use
            messages (list): List of message objects with role and content
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Temperature for sampling
            **kwargs: Additional parameters to pass to the API

        Returns:
            dict: The API response
        """
        data = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in ["model", "messages", "max_tokens", "temperature"]:
                data[key] = value

        self.logger.info(f"Sending request to OpenRouter API: {json.dumps(data)[:200]}...")

        try:
            response = requests.post(self.api_url, headers=self.headers, json=data)
            response.raise_for_status()  # Raise an exception for 4XX/5XX responses

            result = response.json()
            self.logger.info(f"Received response from OpenRouter API: {json.dumps(result)[:200]}...")

            # Create a response object that mimics the OpenAI library response
            class ChatCompletionResponse:
                def __init__(self, response_data):
                    self.id = response_data.get("id", "")
                    self.model = response_data.get("model", "")
                    self.object = response_data.get("object", "")
                    self.created = response_data.get("created", 0)
                    self.choices = []

                    for choice in response_data.get("choices", []):
                        message_content = choice.get("message", {}).get("content", "")
                        self.choices.append(ChatCompletionChoice(choice.get("index", 0), message_content))

                    self.usage = response_data.get("usage", {})

            class ChatCompletionChoice:
                def __init__(self, index, content):
                    self.index = index
                    self.message = ChatCompletionMessage(content)
                    self.finish_reason = "stop"

            class ChatCompletionMessage:
                def __init__(self, content):
                    self.content = content
                    self.role = "assistant"

            return ChatCompletionResponse(result)

        except Exception as e:
            self.logger.error(f"Error calling OpenRouter API: {str(e)}")
            raise e

    def test_connection(self):
        """Test the connection to the OpenRouter API"""
        try:
            response = self.chat_completion_create(
                model="openai/gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Say 'API is working' in 5 words or less"}],
                max_tokens=3
            )

            if response and response.choices and len(response.choices) > 0:
                return response.choices[0].message.content
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error testing OpenRouter API connection: {str(e)}")
            return None

// Background script for SarcasmoShot Chrome Extension

// Create context menu when extension is installed
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: "make-it-sarcastic",
    title: "🎭 Make it Sarcastic",
    contexts: ["selection"]
  });
  
  console.log("SarcasmoShot extension installed and context menu created");
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "make-it-sarcastic" && info.selectionText) {
    console.log("Context menu clicked with selected text:", info.selectionText);
    
    // Send the selected text to content script to show modal
    chrome.tabs.sendMessage(tab.id, {
      action: "showSarcasticModal",
      selectedText: info.selectionText
    });
  }
});

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "getSarcasticText") {
    handleSarcasticRequest(request.text, request.sarcasmLevel)
      .then(response => {
        sendResponse({ success: true, data: response });
      })
      .catch(error => {
        console.error("Error getting sarcastic text:", error);
        sendResponse({ 
          success: false, 
          error: "Sarcasm engine overloaded. Try again." 
        });
      });
    
    // Return true to indicate we'll send a response asynchronously
    return true;
  }
});

// Function to call the sarcastic AI bot API
async function handleSarcasticRequest(text, sarcasmLevel = "medium") {
  try {
    // Get the API endpoint from storage or use default
    const result = await chrome.storage.sync.get(['apiEndpoint']);
    const apiEndpoint = result.apiEndpoint || 'http://127.0.0.1:5000/api/sarcasm';
    
    console.log("Sending request to:", apiEndpoint);
    console.log("Text:", text);
    console.log("Sarcasm level:", sarcasmLevel);
    
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: text,
        sarcasm_level: sarcasmLevel
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log("API response:", data);
    
    return data;
  } catch (error) {
    console.error("API request failed:", error);
    throw error;
  }
}

// Handle extension icon click (optional - opens popup)
chrome.action.onClicked.addListener((tab) => {
  console.log("Extension icon clicked");
});

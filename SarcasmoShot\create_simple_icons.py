"""
Simple script to create basic PNG icons for the Chrome extension
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available. Install with: pip install Pillow")

import os

def create_simple_icon(size, filename):
    """Create a simple icon with text"""
    if not PIL_AVAILABLE:
        print(f"Cannot create {filename} - PIL not available")
        return
    
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Background circle with gradient-like effect
    for i in range(size//4):
        alpha = int(255 * (1 - i/(size//4)))
        color = (102, 126, 234, alpha)  # Blue with varying alpha
        draw.ellipse([i, i, size-i, size-i], fill=color)
    
    # Draw main circle
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(102, 126, 234, 255), outline=(118, 75, 162, 255), width=max(1, size//32))
    
    # Draw sarcastic face
    if size >= 32:
        # Eyes
        eye_size = max(2, size // 16)
        eye_y = size // 3
        draw.ellipse([size//3 - eye_size, eye_y - eye_size, size//3 + eye_size, eye_y + eye_size], 
                    fill=(255, 255, 255, 255))
        draw.ellipse([2*size//3 - eye_size, eye_y - eye_size, 2*size//3 + eye_size, eye_y + eye_size], 
                    fill=(255, 255, 255, 255))
        
        # Sarcastic smirk
        mouth_y = 2*size//3
        mouth_width = size//3
        draw.arc([size//2 - mouth_width//2, mouth_y - mouth_width//4, 
                 size//2 + mouth_width//2, mouth_y + mouth_width//4], 
                start=0, end=180, fill=(255, 255, 255, 255), width=max(1, size//32))
    
    # Add "S" for SarcasmoShot if size is large enough
    if size >= 48:
        try:
            # Try to use a font
            font_size = max(12, size // 4)
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
        
        # Draw "S" in the center
        text = "S"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (size - text_width) // 2
        y = (size - text_height) // 2
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def create_all_icons():
    """Create all required icon sizes"""
    # Create icons directory if it doesn't exist
    icons_dir = "icons"
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = os.path.join(icons_dir, f"icon{size}.png")
        create_simple_icon(size, filename)
    
    print("\nAll icons created successfully!")
    print("You can now update the manifest.json to include icon references.")

if __name__ == "__main__":
    if PIL_AVAILABLE:
        create_all_icons()
    else:
        print("To create icons, install Pillow: pip install Pillow")
        print("Then run this script again.")
        print("\nAlternatively, the extension will work without icons.")

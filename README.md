# Sarcastic Bot with Real-Time News Integration

A witty, sarcastic chatbot with a beautiful UI that runs locally using LLMs, can be shared with others via a link, and works on other systems locally without deployment.

## Features

- **Sarcastic Personality**: Enjoy witty, varied responses with adjustable sarcasm levels (mild, medium, extreme)
- **Real-Time Information**: Fetches current news about topics and personalities using Google News RSS feeds
- **Personality Switching**: Can dynamically mimic famous personalities (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>)
- **Beautiful UI**: Modern, responsive design with animations and visual feedback
- **Dark/Light Mode**: Toggle between themes with persistent preferences
- **Voice Input/Output**: Speak to the bot and have responses read aloud
- **Google Login**: Authenticate using Google accounts
- **Trending News**: Fetches and incorporates trending news into responses

## Quick Start (For Users)

### Windows Users

1. Download the ZIP file from the provided link
2. Extract the ZIP file to a folder on your computer
3. Double-click the `run_sarcastic_bot.bat` file
4. Follow the on-screen instructions to set up your OpenAI API key
5. Enjoy the sarcastic bot!

### macOS/Linux Users

1. Download the ZIP file from the provided link
2. Extract the ZIP file to a folder on your computer
3. Open Terminal and navigate to the extracted folder
4. Make the run script executable: `chmod +x run_sarcastic_bot.sh`
5. Run the script: `./run_sarcastic_bot.sh`
6. Follow the on-screen instructions to set up your OpenAI API key
7. Enjoy the sarcastic bot!

## Sharing with Others

To share the Sarcastic Bot with others:

1. Create a ZIP file of the entire project folder
2. Share the ZIP file via email, file sharing service, or USB drive
3. Instruct recipients to follow the Quick Start instructions above

When running the bot, it will display a URL that can be accessed by other devices on your local network. Share this URL with others to let them interact with your bot from their devices.

## Requirements

- Python 3.8 or higher
- Internet connection (for API access)
- OpenRouter API key (get one at https://openrouter.ai/)
- Google OAuth credentials (optional, for Google Login)

## API Keys

### OpenRouter API Key

The Sarcastic Bot now uses OpenRouter's API to generate responses. You'll need an API key to use it:

1. Create an account at https://openrouter.ai/
2. Navigate to API keys: https://openrouter.ai/keys
3. Create a new API key
4. Copy the key and paste it in the `.env` file as `OPENROUTER_API_KEY`

### Google OAuth (Optional)

For Google Login functionality:

1. Create a project in Google Cloud Console
2. Enable the Google OAuth API
3. Create OAuth credentials
4. Add the credentials to your `.env` file as `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`

## Troubleshooting

- **API errors**: If you see errors about API quota or invalid API key, check your OpenRouter account for billing information or try a different API key.
- **Browser issues**: If the browser doesn't open automatically, manually navigate to the URL shown in the terminal (usually http://127.0.0.1:8000).
- **Network sharing**: If others can't access your bot on the network, check your firewall settings to ensure the port (8000) is allowed.

## Usage

1. Login options:
   - Use Google Login (if configured)
   - Use demo credentials:
     - Email: `<EMAIL>` / Password: `test123`
     - Email: `<EMAIL>` / Password: `admin123`

2. Chat with the bot:
   - Type messages in the input field
   - Use the microphone button for voice input
   - Use the speaker button to hear responses

3. Personality switching:
   - Use the `/switch` command followed by a personality name:
     ```
     /switch trump
     /switch elon
     /switch modi
     /switch imran
     /switch hitler
     ```
   - Or use natural language:
     ```
     Act like Elon Musk
     Pretend to be Donald Trump
     ```

4. Getting current information:
   - Ask about current events or personalities:
     ```
     What's the latest news about Elon Musk?
     Tell me about the current situation in Ukraine
     What's happening with the stock market today?
     ```

5. Customize your experience:
   - Toggle between dark and light mode
   - Adjust sarcasm level (mild, medium, extreme)
   - Reset the conversation when needed
   - Logout when finished

## License

MIT

## New Feature: Real-Time News Integration

The bot now integrates real-time news from Google News RSS feeds to provide current information about topics and personalities. This allows the bot to:

1. **Stay Current**: Provide up-to-date information about world events, famous personalities, and trending topics
2. **Enhance Personality Mimicry**: When mimicking a personality, the bot can reference current news for more realistic responses
3. **Trending Topics**: Fetch trending news when specific information isn't available

### Implementation Details

- **Google News RSS**: Uses feedparser to fetch news from Google News RSS feeds
- **Trending Topics**: Falls back to predefined categories (World News, Technology, Sports) when specific trending topics aren't available
- **Context-Aware Sarcasm**: Adjusts sarcasm level based on the seriousness of the topic

## Acknowledgements

- OpenRouter for the LLM API
- OpenAI for the GPT models
- Flask for the web framework
- Web Speech API for voice functionality
- Google News RSS for real-time information
- Feedparser for RSS feed parsing

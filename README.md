# Sarcastic Bot

A witty, sarcastic chatbot with a beautiful UI that runs locally using LLMs like GPT-3.5-turbo.

## Features

- **Sarcastic Personality**: Enjoy witty, varied responses with a sarcastic tone
- **Beautiful UI**: Modern, responsive design with animations and visual feedback
- **Dark/Light Mode**: Toggle between themes with persistent preferences
- **Voice Input/Output**: Speak to the bot and have responses read aloud
- **Fake Login System**: Demo login system for a complete application experience

## Quick Start (For Users)

### Windows Users

1. Download the ZIP file from the provided link
2. Extract the ZIP file to a folder on your computer
3. Double-click the `run_sarcastic_bot.bat` file
4. Follow the on-screen instructions to set up your OpenAI API key
5. Enjoy the sarcastic bot!

### macOS/Linux Users

1. Download the ZIP file from the provided link
2. Extract the ZIP file to a folder on your computer
3. Open Terminal and navigate to the extracted folder
4. Make the run script executable: `chmod +x run_sarcastic_bot.sh`
5. Run the script: `./run_sarcastic_bot.sh`
6. Follow the on-screen instructions to set up your OpenAI API key
7. Enjoy the sarcastic bot!

## Sharing with Others

To share the Sarcastic Bot with others:

1. Create a ZIP file of the entire project folder
2. Share the ZIP file via email, file sharing service, or USB drive
3. Instruct recipients to follow the Quick Start instructions above

When running the bot, it will display a URL that can be accessed by other devices on your local network. Share this URL with others to let them interact with your bot from their devices.

## Requirements

- Python 3.8 or higher
- Internet connection (for OpenAI API)
- OpenAI API key (get one at https://platform.openai.com/api-keys)

## OpenAI API Key

The Sarcastic Bot uses OpenAI's API to generate responses. You'll need an API key to use it:

1. Create an account at https://platform.openai.com/
2. Navigate to API keys: https://platform.openai.com/api-keys
3. Create a new API key
4. Copy the key and paste it in the `.env` file when prompted

## Troubleshooting

- **OpenAI API errors**: If you see errors about API quota or invalid API key, check your OpenAI account for billing information or try a different API key.
- **Browser issues**: If the browser doesn't open automatically, manually navigate to the URL shown in the terminal (usually http://127.0.0.1:8000).
- **Network sharing**: If others can't access your bot on the network, check your firewall settings to ensure the port (8000) is allowed.

## Usage

1. Login with demo credentials:
   - Email: `<EMAIL>` / Password: `test123`
   - Email: `<EMAIL>` / Password: `admin123`

2. Chat with the bot:
   - Type messages in the input field
   - Use the microphone button for voice input
   - Use the speaker button to hear responses

3. Customize your experience:
   - Toggle between dark and light mode
   - Reset the conversation when needed
   - Logout when finished

## License

MIT

## Acknowledgements

- OpenAI for the GPT-3.5-turbo API
- Flask for the web framework
- Web Speech API for voice functionality

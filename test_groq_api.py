"""
Test script for the Groq API integration

This script tests the DirectGroq client to ensure it can connect to the Groq API
and generate responses correctly.
"""

import os
import json
import logging
from dotenv import load_dotenv
from direct_groq import DirectGroq

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get API key from environment
api_key = os.getenv("GROQ_API_KEY", "********************************************************")
print(f"Using Groq API key: {api_key[:10]}...")

# Create a direct client for API calls
client = DirectGroq(api_key, logger=logger)

# Test with a simple request
def test_simple_request():
    print("Testing simple request...")

    try:
        response = client.chat_completion_create(
            messages=[
                {"role": "user", "content": "Say 'API is working' in 5 words or less"}
            ],
            max_tokens=10
        )

        print(f"Response received successfully")

        if response and response.choices and len(response.choices) > 0:
            message = response.choices[0].message.content
            print(f"Message: {message}")
            return True
        else:
            print("No choices in response")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Test with a more complex request
def test_complex_request():
    print("\nTesting complex request...")

    try:
        response = client.chat_completion_create(
            messages=[
                {"role": "system", "content": "You are a helpful assistant that provides accurate information."},
                {"role": "user", "content": "Tell me about the latest technology trends in 2025"}
            ],
            max_tokens=100
        )

        print(f"Response received successfully")

        if response and response.choices and len(response.choices) > 0:
            message = response.choices[0].message.content
            print(f"Message: {message}")
            return True
        else:
            print("No choices in response")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

# Test with a personality prompt
def test_personality_prompt():
    print("\nTesting personality prompt...")

    try:
        trump_prompt = """You are now speaking exactly like Donald Trump. Follow these specific language patterns:

VOCABULARY:
- Use simple, repetitive words: "tremendous", "huge", "amazing", "disaster", "terrible"
- Superlatives: "the best", "the greatest", "the worst", "like nobody's ever seen before"
- Self-references: "believe me", "that I can tell you", "everyone says", "a lot of people are saying"
- Exaggerations: "millions and millions", "billions and billions", "like never before"
- Nicknames for opponents: "Sleepy Joe", "Crooked Hillary", "Lyin' Ted"

SENTENCE STRUCTURE:
- Short, simple sentences
- Sentence fragments
- Repetition for emphasis
- Rhetorical questions: "You know what?", "Can you believe it?", "Isn't that terrible?"
- Tangents and non-sequiturs

SPEECH PATTERNS:
- Start with "Look," or "Listen,"
- Use "very, very" instead of more sophisticated adverbs
- Interrupt yourself with asides
- Use "by the way" frequently
- End with strong declarative statements: "Sad!", "So true!", "Disgraceful!"

TONE:
- Boastful about accomplishments
- Dismissive of critics
- Hyperbolic in all descriptions
- Combative when challenged
- Nostalgic about the past

CONTENT APPROACH:
- Focus on winners vs. losers
- Emphasize size, ratings, and numbers
- Reference personal wealth and success
- Make bold, absolute claims
- Position yourself as a victim of unfair treatment

DO NOT use any emojis, winks, or throat clearing expressions. Just speak naturally as Trump would."""

        response = client.chat_completion_create(
            model="llama3-70b-8192",
            messages=[
                {"role": "system", "content": trump_prompt},
                {"role": "user", "content": "What do you think about the economy right now?"}
            ],
            max_tokens=150,
            temperature=0.9
        )

        print(f"Response received successfully")

        if response and response.choices and len(response.choices) > 0:
            message = response.choices[0].message.content
            print(f"Trump response: {message}")
            return True
        else:
            print("No choices in response")
            return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing Groq API...")
    simple_test_result = test_simple_request()
    complex_test_result = test_complex_request()
    personality_test_result = test_personality_prompt()

    if simple_test_result and complex_test_result and personality_test_result:
        print("\nAll tests passed!")
    else:
        print("\nSome tests failed.")

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --accent-color: #fd79a8;
            --text-color: #2d3436;
            --bg-color: #f5f6fa;
            --card-bg: #ffffff;
            --header-bg: #6c5ce7;
            --header-text: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --accent-color: #fd79a8;
            --text-color: #f5f6fa;
            --bg-color: #2d3436;
            --card-bg: #353b48;
            --header-bg: #4834d4;
            --header-text: #f5f6fa;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .header {
            background-color: var(--header-bg);
            color: var(--header-text);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
        }

        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .user-name {
            font-weight: bold;
            margin-right: 15px;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .theme-toggle, .back-btn {
            background: none;
            border: none;
            color: var(--header-text);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .theme-toggle:hover, .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .section {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
        }

        .section h2 {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }

        .conversation-list {
            list-style: none;
            padding: 0;
        }

        .conversation-item {
            background-color: rgba(108, 92, 231, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: transform 0.2s;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversation-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1);
        }

        .conversation-info {
            flex: 1;
        }

        .conversation-title {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .conversation-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.9rem;
            color: #666;
        }

        .conversation-meta span {
            display: flex;
            align-items: center;
        }

        .conversation-meta i {
            margin-right: 5px;
        }

        .sarcasm-score {
            display: flex;
            align-items: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .score-badge {
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .leaderboard-table {
            width: 100%;
            border-collapse: collapse;
        }

        .leaderboard-table th, .leaderboard-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .leaderboard-table th {
            background-color: rgba(108, 92, 231, 0.1);
            color: var(--primary-color);
        }

        .leaderboard-table tr:hover {
            background-color: rgba(108, 92, 231, 0.05);
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        @media (max-width: 768px) {
            .conversation-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .sarcasm-score {
                margin-top: 1rem;
            }

            .conversation-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="user-info">
                {% if user_picture %}
                <img src="{{ user_picture }}" alt="Profile" class="user-avatar">
                {% endif %}
                {% if user_name %}
                <span class="user-name">{{ user_name }}</span>
                {% else %}
                <span class="user-name">{{ user_email }}</span>
                {% endif %}
            </div>
            <h1>Conversation History</h1>
        </div>
        <div class="header-controls">
            <button class="theme-toggle" id="theme-toggle" title="Toggle Dark/Light Mode">
                <i class="fas fa-moon"></i>
            </button>
            <button class="back-btn" id="back-btn" title="Back to Chat">
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h2>Your Recent Conversations</h2>
            {% if conversations %}
            <ul class="conversation-list">
                {% for conversation in conversations %}
                <li class="conversation-item" onclick="window.location.href='/conversation/{{ conversation.id }}'">
                    <div class="conversation-info">
                        <div class="conversation-title">
                            {% if conversation.topic %}
                                {{ conversation.topic }}
                            {% else %}
                                Conversation #{{ conversation.id }}
                            {% endif %}
                        </div>
                        <div class="conversation-meta">
                            <span><i class="far fa-calendar-alt"></i> {{ conversation.timestamp }}</span>
                            <span><i class="fas fa-user-circle"></i> 
                                {% if conversation.personality %}
                                    {{ conversation.personality }} sarcasm
                                {% else %}
                                    Default personality
                                {% endif %}
                            </span>
                            {% if conversation.behavior_personality %}
                            <span><i class="fas fa-theater-masks"></i> {{ conversation.behavior_personality }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="sarcasm-score">
                        <div class="score-badge">{{ conversation.sarcasm_score }}</div>
                    </div>
                </li>
                {% endfor %}
            </ul>
            {% else %}
            <div class="empty-state">
                <i class="far fa-comment-dots"></i>
                <p>You haven't had any conversations yet. Go chat with the bot!</p>
            </div>
            {% endif %}
        </div>

        <div class="section">
            <h2>Sarcasm Leaderboard</h2>
            {% if top_users %}
            <table class="leaderboard-table">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>User</th>
                        <th>Avg. Sarcasm</th>
                        <th>Highest Score</th>
                        <th>Conversations</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in top_users %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.avg_sarcasm_score|round(1) }}</td>
                        <td>{{ user.highest_sarcasm_score }}</td>
                        <td>{{ user.total_conversations }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="empty-state">
                <i class="fas fa-trophy"></i>
                <p>No users on the leaderboard yet. Be the first!</p>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            const icon = themeToggle.querySelector('i');

            // Load theme from localStorage
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);

            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            });

            function updateThemeIcon(theme) {
                if (theme === 'dark') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            }

            // Back button functionality
            document.getElementById('back-btn').addEventListener('click', function() {
                window.location.href = '/';
            });
        });
    </script>
</body>
</html>

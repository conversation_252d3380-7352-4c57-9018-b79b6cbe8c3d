"""
Test script for the /api/sarcasm endpoint
"""

import requests
import json

def test_api_endpoint():
    url = "http://127.0.0.1:5000/api/sarcasm"
    
    data = {
        "text": "This is a great day",
        "sarcasm_level": "medium"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Testing API endpoint: {url}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Sarcastic response: {result.get('response', 'No response')}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing API: {str(e)}")

if __name__ == "__main__":
    test_api_endpoint()

"""
Production-ready script to run the Sarcastic Bot locally.
Uses waitress as a production WSGI server.
"""

import os
import sys
import socket
import argparse
from waitress import serve
from app import app

# Parse command line arguments
parser = argparse.ArgumentParser(description='Run Sarcastic Bot in production mode')
parser.add_argument('--network', action='store_true', help='Make the app available on the local network')
parser.add_argument('--port', type=int, default=8000, help='Port to run the server on (default: 8000)')
args = parser.parse_args()

def get_local_ip():
    """Get the local IP address of the machine."""
    try:
        # Create a socket to determine the local IP address
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Doesn't need to be reachable
        s.connect(('*******', 1))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return '127.0.0.1'

if __name__ == '__main__':
    # Set host based on arguments
    host = '0.0.0.0' if args.network else '127.0.0.1'
    port = args.port
    
    # Print startup message
    print("\n" + "=" * 60)
    print("Starting Sarcastic Bot in production mode...")
    print("=" * 60)
    
    if args.network:
        local_ip = get_local_ip()
        print(f"\n🌐 App will be available on your local network at:")
        print(f"   http://{local_ip}:{port}")
        print("\n⚠️  Note: Other devices on your network will be able to access the app.")
    else:
        print(f"\n🚀 App will be available locally at:")
        print(f"   http://127.0.0.1:{port}")
        print("\n💡 Tip: Use --network flag to make it available on your local network.")
    
    print("\n📋 Press Ctrl+C to stop the server")
    print("=" * 60 + "\n")
    
    # Start the server
    try:
        serve(app, host=host, port=port, threads=4)
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("Sarcastic Bot server stopped")
        print("=" * 60 + "\n")
        sys.exit(0)

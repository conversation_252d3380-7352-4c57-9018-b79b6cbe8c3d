#!/usr/bin/env python
"""
Script to create a distributable package of the Sarcastic Bot.
This script will:
1. Create a ZIP file with all necessary files
2. Exclude unnecessary files like __pycache__, .git, etc.
"""

import os
import sys
import zipfile
import datetime

# Files and directories to exclude
EXCLUDE = [
    "__pycache__",
    ".git",
    ".gitignore",
    "venv",
    "dist",
    "build",
    "*.egg-info",
    ".env",
    ".vscode",
    ".idea",
    "*.pyc",
    "*.pyo",
    "*.pyd",
    "*.so",
    "*.dll",
    "*.exe",
    "create_package.py",  # Exclude this script itself
]

def should_exclude(path):
    """Check if a path should be excluded."""
    for pattern in EXCLUDE:
        if pattern in path:
            return True
    return False

def create_zip():
    """Create a ZIP file with all necessary files."""
    # Get the current date for the filename
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    zip_filename = f"sarcastic_bot_{today}.zip"
    
    print(f"Creating ZIP file: {zip_filename}")
    
    # Create a ZIP file
    with zipfile.ZipFile(zip_filename, "w", zipfile.ZIP_DEFLATED) as zipf:
        # Walk through all files and directories
        for root, dirs, files in os.walk("."):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not should_exclude(os.path.join(root, d))]
            
            # Add files
            for file in files:
                file_path = os.path.join(root, file)
                if not should_exclude(file_path):
                    # Add the file to the ZIP
                    arcname = file_path[2:]  # Remove the leading "./"
                    zipf.write(file_path, arcname)
    
    # Get the full path to the ZIP file
    zip_path = os.path.abspath(zip_filename)
    
    print(f"\nPackage created successfully: {zip_path}")
    print("\nYou can now share this ZIP file with others.")
    print("They can extract it and run the bot using the run_sarcastic_bot.bat (Windows)")
    print("or run_sarcastic_bot.sh (macOS/Linux) script.")

def main():
    """Main function."""
    print("=" * 60)
    print("Sarcastic Bot Packager")
    print("=" * 60)
    
    try:
        create_zip()
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

"""
Test script for the tweet fetcher module

This script tests the tweet_fetcher module to ensure it can fetch tweets
and select one randomly for the bot to comment on.
"""

import logging
from tweet_fetcher import get_tweets_about, get_tweet_for_personality

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_get_tweets_about():
    """Test fetching tweets about a specific topic"""
    print("\nTesting get_tweets_about...")
    
    # Test with a popular topic that should have tweets
    topics = ["Elon Musk", "climate change", "artificial intelligence", "sports"]
    
    for topic in topics:
        print(f"\nTrying topic: {topic}")
        tweet = get_tweets_about(topic, max_results=5)
        
        if tweet:
            print(f"✅ Found tweet about {topic}:")
            print(f"Username: @{tweet['username']}")
            print(f"Content: {tweet['content']}")
            print(f"URL: {tweet['url']}")
            return True
        else:
            print(f"❌ No tweets found for {topic}")
    
    print("❌ Could not find tweets for any test topic")
    return False

def test_get_tweet_for_personality():
    """Test fetching tweets for different personalities"""
    print("\nTesting get_tweet_for_personality...")
    
    personalities = ["trump", "imran khan", "modi", "elon musk"]
    
    for personality in personalities:
        print(f"\nTrying personality: {personality}")
        tweet = get_tweet_for_personality(personality)
        
        if tweet:
            print(f"✅ Found tweet for {personality}:")
            print(f"Username: @{tweet['username']}")
            print(f"Content: {tweet['content']}")
            print(f"URL: {tweet['url']}")
            return True
        else:
            print(f"❌ No tweets found for personality: {personality}")
    
    print("❌ Could not find tweets for any personality")
    return False

def test_tweet_integration_with_groq():
    """Test integrating a tweet with a Groq API prompt"""
    try:
        from direct_groq import DirectGroq
        import os
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        
        # Get API key from environment
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key:
            print("❌ No GROQ_API_KEY found in environment variables")
            return False
        
        print("\nTesting tweet integration with Groq API...")
        
        # Get a tweet for Trump
        personality = "trump"
        tweet = get_tweet_for_personality(personality)
        
        if not tweet:
            print(f"❌ No tweet found for {personality}")
            return False
        
        # Create a prompt with the tweet
        trump_prompt = """You are now roleplaying as Donald Trump.

Stay fully in character — sarcastic, witty, and exaggerated, just like Donald Trump is known publicly.

Here is a recent tweet about a topic:
"""
        tweet_content = f"@{tweet['username']}: {tweet['content']}"
        trump_prompt += f'"{tweet_content}"\n\n'
        trump_prompt += """React to this tweet with Donald Trump's characteristic style. Be sarcastic, witty, and exaggerated in your response, just like Donald Trump would be in real life. You are not an assistant or bot — you're Donald Trump commenting on this tweet.

IMPORTANT: Keep your response concise (1-3 sentences). DO NOT use any emojis, winks, or throat clearing expressions. Just speak naturally as Trump would."""
        
        # Create Groq client
        client = DirectGroq(api_key, logger=logger)
        
        # Call the API
        response = client.chat_completion_create(
            messages=[
                {"role": "system", "content": trump_prompt},
                {"role": "user", "content": "What's your reaction to this tweet?"}
            ],
            max_tokens=150,
            temperature=0.9
        )
        
        if response and response.choices and len(response.choices) > 0:
            message = response.choices[0].message.content
            print(f"\nTweet: {tweet_content}")
            print(f"\nTrump's response: {message}")
            return True
        else:
            print("❌ No response from Groq API")
            return False
    
    except Exception as e:
        print(f"❌ Error in test_tweet_integration_with_groq: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing tweet fetcher...")
    
    # Test fetching tweets
    tweets_test_result = test_get_tweets_about()
    
    # Test fetching tweets for personalities
    personality_test_result = test_get_tweet_for_personality()
    
    # Test integration with Groq API
    integration_test_result = test_tweet_integration_with_groq()
    
    # Print summary
    print("\n=== Test Results ===")
    print(f"Fetching tweets: {'✅ Passed' if tweets_test_result else '❌ Failed'}")
    print(f"Personality tweets: {'✅ Passed' if personality_test_result else '❌ Failed'}")
    print(f"Groq API integration: {'✅ Passed' if integration_test_result else '❌ Failed'}")
    
    if tweets_test_result and personality_test_result and integration_test_result:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed.")

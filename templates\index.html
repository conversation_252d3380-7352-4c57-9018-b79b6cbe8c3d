<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Light theme (default) */
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --accent-color: #fd79a8;
            --background-color: #f5f6fa;
            --chat-user-bg: #74b9ff;
            --chat-bot-bg: #a29bfe;
            --text-color: #2d3436;
            --light-text: #636e72;
            --card-bg: #ffffff;
            --input-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, #6c5ce7, #a29bfe);
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --primary-color: #9f7aea;
            --secondary-color: #b794f4;
            --accent-color: #f687b3;
            --background-color: #1a202c;
            --chat-user-bg: #4299e1;
            --chat-bot-bg: #9f7aea;
            --text-color: #e2e8f0;
            --light-text: #a0aec0;
            --card-bg: #2d3748;
            --input-bg: #4a5568;
            --header-bg: linear-gradient(135deg, #6b46c1, #9f7aea);
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            flex-direction: column;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .header {
            background: var(--header-bg);
            color: white;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 10px var(--shadow-color);
            position: relative;
        }

        .header-controls {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.8rem;
        }

        .theme-toggle, .settings-toggle, .feedback-toggle {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .theme-toggle:hover, .settings-toggle:hover, .feedback-toggle:hover {
            transform: rotate(30deg);
        }

        /* Settings Panel */
        .settings-panel, .feedback-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 5px 20px var(--shadow-color);
            width: 90%;
            max-width: 500px;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .settings-panel.active, .feedback-panel.active {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .settings-header, .feedback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--input-bg);
        }

        .settings-content, .feedback-content {
            padding: 1.5rem;
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 1.2rem;
            cursor: pointer;
            transition: color 0.2s;
        }

        .close-btn:hover {
            color: var(--primary-color);
        }

        .setting-group {
            margin-bottom: 1.5rem;
        }

        .setting-group label {
            display: block;
            margin-bottom: 0.8rem;
            font-weight: bold;
            color: var(--text-color);
        }

        .personality-options {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .personality-btn {
            padding: 0.6rem 1rem;
            background-color: var(--input-bg);
            border: 2px solid transparent;
            border-radius: 5px;
            color: var(--text-color);
            cursor: pointer;
            transition: all 0.2s;
        }

        .personality-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .personality-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .voice-settings {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .setting-option {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .setting-option label {
            margin-bottom: 0;
            min-width: 100px;
        }

        .setting-option input[type="range"] {
            flex: 1;
        }

        /* Model selection */
        .model-selection {
            margin-bottom: 1rem;
        }

        .model-select {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid var(--input-bg);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 1rem;
            cursor: pointer;
        }

        .model-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .api-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--light-text);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #f1c40f; /* Yellow for loading */
        }

        .status-indicator.online {
            background-color: #2ecc71; /* Green for online */
        }

        .status-indicator.offline {
            background-color: #e74c3c; /* Red for offline */
        }

        /* Feedback Panel */
        .rating-container {
            margin-bottom: 1.5rem;
        }

        .star-rating {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            font-size: 1.5rem;
        }

        .star-rating i {
            cursor: pointer;
            color: var(--light-text);
            transition: color 0.2s;
        }

        .star-rating i:hover, .star-rating i.active {
            color: #f1c40f;
        }

        .feedback-text {
            margin-bottom: 1.5rem;
        }

        .feedback-text label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .feedback-text textarea {
            width: 100%;
            height: 100px;
            padding: 0.8rem;
            border: 2px solid var(--input-bg);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            resize: vertical;
            font-family: inherit;
        }

        .feedback-text textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .submit-feedback-btn {
            width: 100%;
            padding: 0.8rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .submit-feedback-btn:hover {
            background-color: var(--secondary-color);
        }

        .feedback-message {
            margin-top: 1rem;
            padding: 0.8rem;
            border-radius: 5px;
            text-align: center;
            display: none;
        }

        .feedback-message.success {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
            display: block;
        }

        .feedback-message.error {
            background-color: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            display: block;
        }

        /* Commentary */
        .commentary {
            display: none;
            padding: 0.8rem 1.2rem;
            background-color: var(--accent-color);
            color: white;
            border-radius: 10px;
            margin-bottom: 1rem;
            text-align: center;
            animation: fadeIn 0.5s ease-in-out;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--primary-color);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 5px;
            box-shadow: 0 2px 10px var(--shadow-color);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
            text-align: center;
            pointer-events: none;
        }

        .notification.show {
            opacity: 1;
        }

        .user-info {
            position: absolute;
            top: 1rem;
            left: 1rem;
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: white;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin-right: 0.5rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-name {
            margin-right: 0.5rem;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .logout-btn {
            background: none;
            border: none;
            color: white;
            margin-left: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .logout-btn:hover {
            opacity: 1;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1rem;
            opacity: 0.9;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 1rem;
            overflow: hidden;
        }

        .chat-box {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 2px 10px var(--shadow-color);
            margin-bottom: 1rem;
            transition: background-color 0.3s ease;
        }

        .message {
            max-width: 80%;
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            margin-bottom: 0.5rem;
            word-wrap: break-word;
            position: relative;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            align-self: flex-end;
            background-color: var(--chat-user-bg);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .bot-message {
            align-self: flex-start;
            background-color: var(--chat-bot-bg);
            color: white;
            border-bottom-left-radius: 5px;
        }

        .bot-message em {
            font-style: italic;
            opacity: 0.8;
            display: block;
            margin-top: 8px;
            font-size: 0.9em;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 8px;
        }

        .message-time {
            font-size: 0.7rem;
            opacity: 0.7;
            margin-top: 0.3rem;
            text-align: right;
        }

        .input-area {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 2px 10px var(--shadow-color);
            transition: background-color 0.3s ease;
        }

        #message-input {
            flex: 1;
            padding: 0.8rem 1rem;
            border: none;
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        #message-input:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        .voice-controls {
            display: flex;
            gap: 0.5rem;
        }

        .send-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 0.8rem 1.5rem;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .send-btn:hover {
            background-color: var(--secondary-color);
        }

        .reset-btn {
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .voice-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 5px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .voice-btn:hover {
            background-color: var(--primary-color);
        }

        .voice-btn.listening {
            animation: pulse 1.5s infinite;
            background-color: var(--accent-color);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .typing-indicator {
            display: none;
            align-self: flex-start;
            background-color: var(--chat-bot-bg);
            color: white;
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            border-bottom-left-radius: 5px;
            margin-bottom: 0.5rem;
        }

        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: white;
            border-radius: 50%;
            margin-right: 5px;
            animation: typing 1s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
            margin-right: 0;
        }

        @keyframes typing {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .welcome-message {
            text-align: center;
            color: var(--light-text);
            margin: 2rem 0;
        }

        .reset-tip {
            margin-top: 1rem;
            font-size: 0.9rem;
            padding: 0.75rem;
            background-color: rgba(52, 152, 219, 0.1);
            border-left: 3px solid rgba(52, 152, 219, 0.5);
            border-radius: 3px;
            text-align: left;
        }

        /* API Status Styles */
        .api-status {
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #ccc;
        }

        .status-indicator.online {
            background-color: #4CAF50;
        }

        .status-indicator.offline {
            background-color: #f44336;
        }

        .status-text {
            font-size: 0.8rem;
            color: var(--light-text);
        }

        .api-usage {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: rgba(0, 0, 0, 0.05);
            border-radius: 5px;
        }

        .api-usage-bar {
            height: 10px;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            margin: 0.5rem 0;
            overflow: hidden;
        }

        .api-usage-progress {
            height: 100%;
            background-color: #4CAF50;
            border-radius: 5px;
            transition: width 0.3s ease;
        }

        .api-usage-text {
            font-size: 0.8rem;
            color: var(--light-text);
            margin-bottom: 0.5rem;
        }

        .check-api-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.3rem 0.6rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background-color 0.2s;
        }

        .check-api-btn:hover {
            background-color: var(--primary-dark);
        }

        @media (max-width: 600px) {
            .header h1 {
                font-size: 1.8rem;
            }
            .message {
                max-width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="user-info">
            {% if session.user_picture %}
            <img src="{{ session.user_picture }}" alt="Profile" class="user-avatar">
            {% endif %}
            {% if session.user_name %}
            <span class="user-name">{{ session.user_name }}</span>
            {% else %}
            <span class="user-name">{{ user_email }}</span>
            {% endif %}
            <button class="logout-btn" id="logout-btn">Logout</button>
        </div>
        <h1>Sarcastic Bot</h1>
        <p>Prepare for witty comebacks and eye-rolling responses</p>
        <div class="header-controls">
            <button class="theme-toggle" id="theme-toggle" title="Toggle Dark/Light Mode">
                <i class="fas fa-moon"></i>
            </button>
            <button class="settings-toggle" id="settings-toggle" title="Settings">
                <i class="fas fa-cog"></i>
            </button>
            <button class="feedback-toggle" id="feedback-toggle" title="Give Feedback">
                <i class="fas fa-comment-dots"></i>
            </button>
            <button class="history-btn" id="history-btn" title="View Conversation History">
                <i class="fas fa-history"></i>
            </button>
        </div>
    </div>

    <!-- Settings Panel -->
    <div class="settings-panel" id="settings-panel">
        <div class="settings-header">
            <h3>Personality Settings</h3>
            <button class="close-btn" id="close-settings">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="settings-content">
            <div class="setting-group">
                <label>Sarcasm Level:</label>
                <div class="personality-options">
                    <button class="personality-btn" data-personality="mild">Mild (Witty)</button>
                    <button class="personality-btn" data-personality="medium">Medium (Biting)</button>
                    <button class="personality-btn" data-personality="extreme">Extreme (Roast Mode)</button>
                </div>
            </div>
            <div class="setting-group">
                <label>Voice Settings:</label>
                <div class="voice-settings">
                    <div class="setting-option">
                        <label for="voice-rate">Speech Rate:</label>
                        <input type="range" id="voice-rate" min="0.5" max="2" step="0.1" value="1">
                        <span id="voice-rate-value">1.0</span>
                    </div>
                    <div class="setting-option">
                        <label for="voice-pitch">Speech Pitch:</label>
                        <input type="range" id="voice-pitch" min="0.5" max="2" step="0.1" value="1">
                        <span id="voice-pitch-value">1.0</span>
                    </div>
                </div>
            </div>

            <div class="setting-group" id="model-selection-container" style="display: none;">
                <label>AI Model:</label>
                <div class="model-selection">
                    <select id="model-select" class="model-select">
                        <option value="openai/gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    </select>
                </div>
                <div class="api-status" id="api-status">
                    <span class="status-indicator"></span>
                    <span class="status-text">Checking API status...</span>
                </div>
            </div>

            <div class="setting-group">
                <label>API Status:</label>
                <div class="api-usage">
                    <button id="check-api-status" class="check-api-btn">Check API Status</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Feedback Panel -->
    <div class="feedback-panel" id="feedback-panel">
        <div class="feedback-header">
            <h3>Give Feedback</h3>
            <button class="close-btn" id="close-feedback">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="feedback-content">
            <div class="rating-container">
                <label>How would you rate your experience?</label>
                <div class="star-rating">
                    <i class="far fa-star" data-rating="1"></i>
                    <i class="far fa-star" data-rating="2"></i>
                    <i class="far fa-star" data-rating="3"></i>
                    <i class="far fa-star" data-rating="4"></i>
                    <i class="far fa-star" data-rating="5"></i>
                </div>
            </div>
            <div class="feedback-text">
                <label for="feedback-input">Your feedback:</label>
                <textarea id="feedback-input" placeholder="Tell me what you think... I'm dying to know."></textarea>
            </div>
            <button id="submit-feedback" class="submit-feedback-btn">Submit Feedback</button>
            <div id="feedback-message" class="feedback-message"></div>
        </div>
    </div>

    <!-- Notification element -->
    <div class="notification" id="notification"></div>

    <div class="chat-container">
        <div class="chat-box" id="chat-box">
            <div class="welcome-message">
                <h3>Welcome to Sarcastic Bot</h3>
                <p>Go ahead, ask me something. I'm just dying to respond with sarcasm.</p>
            </div>
        </div>

        <div class="typing-indicator" id="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="commentary" id="commentary"></div>

        <div class="input-area">
            <input type="text" id="message-input" placeholder="Type your message here...">
            <div class="voice-controls">
                <button class="voice-btn" id="voice-btn" title="Voice Input">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="voice-btn" id="speak-btn" title="Text to Speech">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
            <button class="send-btn" id="send-btn">Send</button>
            <button class="reset-btn" id="reset-btn">Reset</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const chatBox = document.getElementById('chat-box');
            const messageInput = document.getElementById('message-input');
            const sendBtn = document.getElementById('send-btn');
            const resetBtn = document.getElementById('reset-btn');
            const typingIndicator = document.getElementById('typing-indicator');
            const historyBtn = document.getElementById('history-btn');
            const commentaryElement = document.getElementById('commentary');
            const notificationElement = document.getElementById('notification');
            const themeToggle = document.getElementById('theme-toggle');
            const voiceBtn = document.getElementById('voice-btn');
            const speakBtn = document.getElementById('speak-btn');
            const logoutBtn = document.getElementById('logout-btn');

            // Settings panel elements
            const settingsToggle = document.getElementById('settings-toggle');
            const settingsPanel = document.getElementById('settings-panel');
            const closeSettings = document.getElementById('close-settings');
            const personalityBtns = document.querySelectorAll('.personality-btn');
            const voiceRate = document.getElementById('voice-rate');
            const voiceRateValue = document.getElementById('voice-rate-value');
            const voicePitch = document.getElementById('voice-pitch');
            const voicePitchValue = document.getElementById('voice-pitch-value');

            // Model selection elements
            const modelSelectionContainer = document.getElementById('model-selection-container');
            const modelSelect = document.getElementById('model-select');
            const apiStatus = document.getElementById('api-status');
            const statusIndicator = apiStatus.querySelector('.status-indicator');
            const statusText = apiStatus.querySelector('.status-text');
            let currentModel = 'openai/gpt-3.5-turbo'; // Default model

            // Feedback panel elements
            const feedbackToggle = document.getElementById('feedback-toggle');
            const feedbackPanel = document.getElementById('feedback-panel');
            const closeFeedback = document.getElementById('close-feedback');
            const starRating = document.querySelectorAll('.star-rating i');
            const feedbackInput = document.getElementById('feedback-input');
            const submitFeedback = document.getElementById('submit-feedback');
            const feedbackMessage = document.getElementById('feedback-message');

            // State variables
            let currentPersonality = 'medium'; // Default personality
            let selectedRating = 3; // Default rating
            let isTyping = false;

            // Speech recognition setup
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            let recognition = null;

            if (SpeechRecognition) {
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.lang = 'en-US';

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    messageInput.value = transcript;
                    voiceBtn.classList.remove('listening');
                    // Auto-send after voice input
                    sendMessage(true);
                };

                recognition.onend = function() {
                    voiceBtn.classList.remove('listening');
                };

                recognition.onerror = function(event) {
                    console.error('Speech recognition error', event.error);
                    voiceBtn.classList.remove('listening');
                };
            } else {
                voiceBtn.style.display = 'none';
                console.log('Speech recognition not supported');
            }

            // Text-to-speech setup
            const synth = window.speechSynthesis;
            let currentUtterance = null;

            // Theme management
            function loadTheme() {
                const savedTheme = localStorage.getItem('theme') || 'light';
                document.documentElement.setAttribute('data-theme', savedTheme);
                updateThemeIcon(savedTheme);
            }

            function toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            }

            function updateThemeIcon(theme) {
                const icon = themeToggle.querySelector('i');
                if (theme === 'dark') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            }

            // Load theme on startup
            loadTheme();

            // Load voice settings
            function loadVoiceSettings() {
                const savedRate = localStorage.getItem('voice-rate') || '1.0';
                const savedPitch = localStorage.getItem('voice-pitch') || '1.0';

                voiceRate.value = savedRate;
                voiceRateValue.textContent = savedRate;
                voicePitch.value = savedPitch;
                voicePitchValue.textContent = savedPitch;
            }

            // Load voice settings on startup
            loadVoiceSettings();

            // Check API status and get available models
            function checkApiStatus() {
                statusIndicator.className = 'status-indicator'; // Reset to loading state
                statusText.textContent = 'Checking API status...';

                fetch('/api_status')
                    .then(response => response.json())
                    .then(data => {
                        // Handle OpenRouter API status
                        if (data.openrouter.status === 'success') {
                            // API is online
                            statusIndicator.className = 'status-indicator online';
                            statusText.textContent = 'API is online';
                            modelSelectionContainer.style.display = 'block';

                            // Populate model dropdown if models are available
                            if (data.openrouter.models && data.openrouter.models.length > 0) {
                                // Clear existing options
                                modelSelect.innerHTML = '';

                                // Add models to dropdown
                                data.openrouter.models.forEach(model => {
                                    const option = document.createElement('option');
                                    option.value = model.id;
                                    option.textContent = model.name || model.id;
                                    modelSelect.appendChild(option);
                                });

                                // Set current model if it exists in the list
                                if (localStorage.getItem('selected-model')) {
                                    currentModel = localStorage.getItem('selected-model');
                                    modelSelect.value = currentModel;
                                }
                            }

                            // Show success notification
                            showNotification("API is online and working");
                        } else {
                            // API is offline
                            statusIndicator.className = 'status-indicator offline';
                            statusText.textContent = 'API is offline - using fallback responses';
                            modelSelectionContainer.style.display = 'none';

                            // Show error notification
                            showNotification("API is offline - using fallback responses");
                        }
                    })
                    .catch(error => {
                        console.error('Error checking API status:', error);
                        statusIndicator.className = 'status-indicator offline';
                        statusText.textContent = 'Error checking API status';
                        modelSelectionContainer.style.display = 'none';

                        // Show error notification
                        showNotification("Error checking API status");
                    });
            }

            // Check API status on startup
            checkApiStatus();

            // Add event listener for the check API status button
            document.getElementById('check-api-status').addEventListener('click', function() {
                checkApiStatus();
                showNotification("Checking API status...");
            });

            // Update personality UI
            function updatePersonalityUI(personality) {
                personalityBtns.forEach(btn => {
                    if (btn.dataset.personality === personality) {
                        btn.classList.add('active');
                    } else {
                        btn.classList.remove('active');
                    }
                });
            }

            // Set initial personality UI
            updatePersonalityUI(currentPersonality);

            // Function to convert simple markdown to HTML
            function markdownToHtml(text) {
                // Handle italics with underscores
                text = text.replace(/_(.*?)_/g, '<em>$1</em>');
                // Handle bold with asterisks
                text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                // Handle newlines
                text = text.replace(/\n/g, '<br>');
                return text;
            }

            // Function to add a message to the chat
            function addMessage(message, sender, timestamp) {
                const messageElement = document.createElement('div');
                messageElement.classList.add('message');
                messageElement.classList.add(sender === 'user' ? 'user-message' : 'bot-message');

                // Use provided timestamp or current time
                const timeString = timestamp || new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                // Convert markdown to HTML for bot messages
                const formattedMessage = sender === 'bot' ? markdownToHtml(message) : message;

                messageElement.innerHTML = `
                    ${formattedMessage}
                    <div class="message-time">${timeString}</div>
                `;

                chatBox.appendChild(messageElement);
                chatBox.scrollTop = chatBox.scrollHeight;

                // If it's a bot message, store it for potential text-to-speech
                // Store the original message without HTML for text-to-speech
                if (sender === 'bot') {
                    messageElement.dataset.text = message;
                }
            }

            // Function to show commentary
            function showCommentary(text) {
                if (!text) return;

                commentaryElement.textContent = text;
                commentaryElement.style.display = 'block';
                setTimeout(() => {
                    commentaryElement.style.display = 'none';
                }, 5000); // Hide after 5 seconds
            }

            // Function to show a temporary notification
            function showNotification(message) {
                if (!message) return;

                notificationElement.textContent = message;
                notificationElement.classList.add('show');

                // Hide notification after 3 seconds
                setTimeout(() => {
                    notificationElement.classList.remove('show');
                }, 3000);
            }

            // Function to show typing indicator
            function showTypingIndicator() {
                isTyping = true;
                typingIndicator.style.display = 'block';
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // Function to hide typing indicator
            function hideTypingIndicator() {
                isTyping = false;
                typingIndicator.style.display = 'none';
            }

            // Personality voice mappings
            const personalityVoices = {
                "trump": { pitch: 0.8, rate: 0.9 },  // Deeper, slower voice for Trump
                "modi": { pitch: 1.0, rate: 0.95 },  // Measured pace for Modi
                "imran khan": { pitch: 1.05, rate: 1.0 }, // Slightly higher pitch for Imran Khan
                "hitler": { pitch: 1.2, rate: 1.1 },  // Higher, faster voice for Hitler
                "elon musk": { pitch: 0.9, rate: 1.05 } // Slightly deeper, faster for Elon Musk
            };

            // Function to speak text
            function speakText(text) {
                // Cancel any ongoing speech
                if (synth.speaking) {
                    synth.cancel();
                }

                // Strip HTML tags for clean speech
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = text;
                const cleanText = tempDiv.textContent || tempDiv.innerText;

                const utterance = new SpeechSynthesisUtterance(cleanText);

                // Get available voices
                const voices = synth.getVoices();

                // Check if we have a behavior personality active
                const behaviorPersonality = '{{ session.get("behavior_personality", "") }}';

                if (behaviorPersonality && personalityVoices[behaviorPersonality.toLowerCase()]) {
                    // Use personality-specific voice settings
                    const voiceSettings = personalityVoices[behaviorPersonality.toLowerCase()];
                    utterance.pitch = voiceSettings.pitch;
                    utterance.rate = voiceSettings.rate;

                    // Log the personality voice being used
                    console.log(`Using ${behaviorPersonality} voice settings: pitch=${utterance.pitch}, rate=${utterance.rate}`);

                    // Try to find a suitable voice for the personality
                    if (voices.length > 0) {
                        // Use different voices for different personalities if available
                        if (behaviorPersonality.toLowerCase() === 'trump' && voices.length > 1) {
                            utterance.voice = voices.find(v => v.name.includes('Male')) || voices[0];
                        } else if (behaviorPersonality.toLowerCase() === 'modi' && voices.length > 2) {
                            utterance.voice = voices.find(v => v.lang.includes('en-IN')) || voices[1];
                        } else if (behaviorPersonality.toLowerCase() === 'imran khan' && voices.length > 3) {
                            utterance.voice = voices.find(v => v.lang.includes('en-GB')) || voices[2];
                        } else if (behaviorPersonality.toLowerCase() === 'hitler' && voices.length > 4) {
                            utterance.voice = voices.find(v => v.lang.includes('de')) || voices[3];
                        } else if (behaviorPersonality.toLowerCase() === 'elon musk' && voices.length > 5) {
                            utterance.voice = voices.find(v => v.name.includes('Male')) || voices[4];
                        }
                    }
                } else {
                    // Use user-defined settings
                    utterance.rate = parseFloat(voiceRate.value);
                    utterance.pitch = parseFloat(voicePitch.value);
                }

                // Show notification about voice
                if (behaviorPersonality) {
                    showNotification(`Speaking as ${behaviorPersonality}`);
                }

                currentUtterance = utterance;
                synth.speak(utterance);
            }

            // Function to send message to server
            function sendMessage(isVoice = false) {
                const message = messageInput.value.trim();
                if (!message) return;

                // Don't allow sending while typing indicator is showing
                if (isTyping) return;

                // Add user message to chat
                addMessage(message, 'user');
                messageInput.value = '';

                // Show typing indicator
                showTypingIndicator();

                // Send message to server
                const formData = new FormData();
                formData.append('message', message);
                formData.append('is_voice', isVoice);
                formData.append('personality', currentPersonality);
                formData.append('model', currentModel); // Include the selected model

                fetch('/chat', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (response.status === 401) {
                        // Redirect to login if session expired
                        window.location.href = '/login';
                        throw new Error('Not logged in');
                    }
                    return response.json();
                })
                .then(data => {
                    // Update current personality if it changed
                    if (data.personality && data.personality !== currentPersonality) {
                        currentPersonality = data.personality;
                        updatePersonalityUI(currentPersonality);
                    }

                    // Simulate typing delay
                    setTimeout(() => {
                        // Hide typing indicator
                        hideTypingIndicator();

                        // Add bot response to chat without any notes
                        addMessage(data.bot_response, 'bot', data.timestamp);

                        // Show commentary if provided
                        if (data.commentary) {
                            setTimeout(() => {
                                showCommentary(data.commentary);
                            }, 1000);
                        }

                        // Auto-speak if this was a voice message or if a personality is active
                        const behaviorPersonality = '{{ session.get("behavior_personality", "") }}';
                        if (isVoice || data.is_voice || behaviorPersonality) {
                            speakText(data.bot_response);
                        }
                    }, data.typing_delay ? data.typing_delay * 1000 : 1000);
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (error.message !== 'Not logged in') {
                        hideTypingIndicator();
                        addMessage("Sorry, I'm having trouble connecting to my brain right now. Try again later, or don't. I don't really care.", 'bot');
                    }
                });
            }

            // Function to set personality
            function setPersonality(personality) {
                if (!personality) return;

                const formData = new FormData();
                formData.append('personality', personality);

                fetch('/set_personality', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        currentPersonality = data.personality;
                        updatePersonalityUI(currentPersonality);

                        // Show a temporary notification instead of adding a message to the chat
                        const messages = {
                            'mild': "Switched to mild sarcasm mode",
                            'medium': "Switched to medium sarcasm mode",
                            'extreme': "Switched to extreme roast mode"
                        };

                        // Create and show a temporary notification
                        showNotification(messages[currentPersonality]);
                    }
                })
                .catch(error => {
                    console.error('Error setting personality:', error);
                });
            }

            // Function to submit feedback
            function submitUserFeedback() {
                const feedbackText = feedbackInput.value.trim();

                if (!feedbackText) {
                    feedbackMessage.textContent = "Please enter some feedback text.";
                    feedbackMessage.className = "feedback-message error";
                    return;
                }

                const formData = new FormData();
                formData.append('feedback', feedbackText);
                formData.append('rating', selectedRating);

                fetch('/feedback', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    feedbackMessage.textContent = data.message;
                    feedbackMessage.className = `feedback-message ${data.status}`;

                    if (data.status === 'success') {
                        // Clear form
                        feedbackInput.value = '';
                        starRating.forEach(star => star.className = 'far fa-star');

                        // Close panel after 3 seconds
                        setTimeout(() => {
                            feedbackPanel.classList.remove('active');
                        }, 3000);
                    }
                })
                .catch(error => {
                    console.error('Error submitting feedback:', error);
                    feedbackMessage.textContent = "Error submitting feedback. Try again later.";
                    feedbackMessage.className = "feedback-message error";
                });
            }

            // Event listeners
            sendBtn.addEventListener('click', () => sendMessage(false));

            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage(false);
                }
            });

            resetBtn.addEventListener('click', function() {
                // Show confirmation dialog
                if (confirm("Reset the chat? All your brilliant conversation will be lost forever. Continue?")) {
                    // Show loading notification
                    showNotification("Erasing your questionable life choices...");

                    fetch('/reset', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            chatBox.innerHTML = `
                                <div class="welcome-message">
                                    <h3>Chat Reset</h3>
                                    <p>Let's start over. I'm sure your questions will be more interesting this time... or not.</p>
                                </div>
                            `;
                            commentaryElement.style.display = 'none';

                            // Show success notification
                            showNotification("Chat reset. Let's see if you can be more interesting this time.");
                        }
                    })
                    .catch(error => {
                        console.error('Reset error:', error);
                        showNotification("Error resetting chat history. Please try again.");
                    });
                }
            });

            themeToggle.addEventListener('click', toggleTheme);

            // Settings panel events
            settingsToggle.addEventListener('click', () => {
                settingsPanel.classList.add('active');
            });

            closeSettings.addEventListener('click', () => {
                settingsPanel.classList.remove('active');
            });

            personalityBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const personality = btn.dataset.personality;
                    setPersonality(personality);
                    settingsPanel.classList.remove('active');
                });
            });

            voiceRate.addEventListener('input', function() {
                voiceRateValue.textContent = this.value;
                localStorage.setItem('voice-rate', this.value);
            });

            voicePitch.addEventListener('input', function() {
                voicePitchValue.textContent = this.value;
                localStorage.setItem('voice-pitch', this.value);
            });

            // Model selection event
            modelSelect.addEventListener('change', function() {
                currentModel = this.value;
                localStorage.setItem('selected-model', currentModel);
                showNotification(`Model changed to ${this.options[this.selectedIndex].text}`);
            });

            // Feedback panel events
            feedbackToggle.addEventListener('click', () => {
                feedbackPanel.classList.add('active');
                feedbackMessage.className = "feedback-message";
                feedbackMessage.textContent = "";
            });

            closeFeedback.addEventListener('click', () => {
                feedbackPanel.classList.remove('active');
            });

            starRating.forEach(star => {
                star.addEventListener('click', () => {
                    const rating = parseInt(star.dataset.rating);
                    selectedRating = rating;

                    // Update UI
                    starRating.forEach(s => {
                        if (parseInt(s.dataset.rating) <= rating) {
                            s.className = 'fas fa-star active';
                        } else {
                            s.className = 'far fa-star';
                        }
                    });
                });
            });

            submitFeedback.addEventListener('click', submitUserFeedback);

            if (recognition) {
                voiceBtn.addEventListener('click', function() {
                    if (synth.speaking) {
                        synth.cancel();
                    }

                    if (voiceBtn.classList.contains('listening')) {
                        recognition.stop();
                        voiceBtn.classList.remove('listening');
                    } else {
                        recognition.start();
                        voiceBtn.classList.add('listening');
                    }
                });
            }

            speakBtn.addEventListener('click', function() {
                // Find the last bot message
                const botMessages = document.querySelectorAll('.bot-message');
                if (botMessages.length > 0) {
                    const lastBotMessage = botMessages[botMessages.length - 1];
                    const text = lastBotMessage.dataset.text;

                    if (synth.speaking) {
                        synth.cancel();
                    } else {
                        speakText(text);
                    }
                }
            });

            // History button event listener
            historyBtn.addEventListener('click', function() {
                // Show a loading notification
                showNotification("Loading conversation history...");

                // Redirect to history page
                window.location.href = '/history';
            });

            logoutBtn.addEventListener('click', function() {
                // Show a loading notification
                showNotification("Logging out...");

                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Clear any local storage data
                        localStorage.removeItem('theme');
                        localStorage.removeItem('voice-rate');
                        localStorage.removeItem('voice-pitch');
                        localStorage.removeItem('selected-model');

                        // Redirect to login page
                        window.location.href = data.redirect || '/login';
                    } else {
                        // If there was an error, try a direct approach
                        window.location.href = '/logout';
                    }
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    // If there was an error, try a direct approach
                    window.location.href = '/logout';
                });
            });

            // Close panels when clicking outside
            window.addEventListener('click', function(e) {
                if (settingsPanel.classList.contains('active') &&
                    !settingsPanel.contains(e.target) &&
                    e.target !== settingsToggle &&
                    !settingsToggle.contains(e.target)) {
                    settingsPanel.classList.remove('active');
                }

                if (feedbackPanel.classList.contains('active') &&
                    !feedbackPanel.contains(e.target) &&
                    e.target !== feedbackToggle &&
                    !feedbackToggle.contains(e.target)) {
                    feedbackPanel.classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>
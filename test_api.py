import os
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key from environment
api_key = os.getenv("OPENROUTER_API_KEY")
print(f"Using API key: {api_key[:10]}...")

# Configure OpenAI with OpenRouter
openai.api_key = api_key
openai.api_base = "https://openrouter.ai/api/v1"

# Test API call
try:
    print("Testing API connection...")
    response = openai.ChatCompletion.create(
        model="anthropic/claude-3-haiku",
        messages=[{"role": "user", "content": "Say 'API is working' in 5 words or less"}],
        max_tokens=10
    )
    
    print(f"Response: {response}")
    print(f"Message content: {response.choices[0].message.content}")
    print("API test successful!")
except Exception as e:
    print(f"API test failed: {str(e)}")

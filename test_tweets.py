import snscrape.modules.twitter as sntwitter

def get_tweets_about(topic, max_results=5):
    """Get recent tweets about a specific topic using snscrape"""
    try:
        print(f"Fetching tweets about: {topic}")
        tweets = []
        for i, tweet in enumerate(sntwitter.TwitterSearchScraper(topic).get_items()):
            if i >= max_results:
                break
            tweets.append(tweet.content)
        
        if tweets:
            return "\n".join([f"- {tweet}" for tweet in tweets])
        else:
            print(f"No tweets found for topic: {topic}")
            return None
    except Exception as e:
        print(f"Error fetching tweets: {str(e)}")
        return None

# Test with a trending topic
topic = "Golden Temple"
tweets = get_tweets_about(topic)

if tweets:
    print("\nTweets about", topic)
    print(tweets)
else:
    print(f"No tweets found for {topic}")

# Try another topic
topic = "Elon Musk"
tweets = get_tweets_about(topic)

if tweets:
    print("\nTweets about", topic)
    print(tweets)
else:
    print(f"No tweets found for {topic}")

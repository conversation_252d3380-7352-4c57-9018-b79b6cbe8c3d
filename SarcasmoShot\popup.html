<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SarcasmoShot</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- Header -->
    <div class="popup-header">
      <div class="popup-title">
        <span class="popup-icon">🎭</span>
        <h1>SarcasmoShot</h1>
      </div>
      <div class="popup-subtitle">Transform text into sarcastic gold</div>
    </div>

    <!-- Main Content -->
    <div class="popup-content">
      <!-- Instructions -->
      <div class="instruction-card">
        <h3>How to use:</h3>
        <ol>
          <li>Select any text on a webpage</li>
          <li>Right-click and choose "🎭 Make it Sarcastic"</li>
          <li>Choose your sarcasm level</li>
          <li>Get your sarcastic rewrite!</li>
        </ol>
      </div>

      <!-- Settings -->
      <div class="settings-card">
        <h3>Settings</h3>
        
        <div class="setting-item">
          <label for="api-endpoint">API Endpoint:</label>
          <input 
            type="text" 
            id="api-endpoint" 
            placeholder="http://127.0.0.1:5000/api/sarcasm"
            class="setting-input"
          >
          <button id="save-endpoint" class="btn btn-secondary">Save</button>
        </div>

        <div class="setting-item">
          <label for="default-sarcasm-level">Default Sarcasm Level:</label>
          <select id="default-sarcasm-level" class="setting-select">
            <option value="mild">😊 Low (Gentle Wit)</option>
            <option value="medium" selected>😏 Medium (Classic Sarcasm)</option>
            <option value="extreme">😈 High (Savage Mode)</option>
          </select>
        </div>
      </div>

      <!-- Quick Test -->
      <div class="test-card">
        <h3>Quick Test</h3>
        <textarea 
          id="test-text" 
          placeholder="Enter some text to test the sarcasm engine..."
          class="test-textarea"
        ></textarea>
        
        <div class="test-controls">
          <select id="test-sarcasm-level" class="test-select">
            <option value="mild">😊 Low</option>
            <option value="medium" selected>😏 Medium</option>
            <option value="extreme">😈 High</option>
          </select>
          <button id="test-sarcasm" class="btn btn-primary">Test Sarcasm</button>
        </div>

        <div id="test-result" class="test-result" style="display: none;">
          <div class="test-result-text" id="test-result-text"></div>
          <button id="copy-test-result" class="btn btn-secondary btn-small">Copy</button>
        </div>

        <div id="test-loading" class="test-loading" style="display: none;">
          <div class="spinner"></div>
          <span>Brewing sarcasm...</span>
        </div>

        <div id="test-error" class="test-error" style="display: none;"></div>
      </div>

      <!-- Status -->
      <div class="status-card">
        <div class="status-item">
          <span class="status-label">Extension Status:</span>
          <span class="status-value status-active">Active</span>
        </div>
        <div class="status-item">
          <span class="status-label">API Status:</span>
          <span class="status-value" id="api-status">Checking...</span>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="popup-footer">
      <div class="footer-links">
        <a href="#" id="help-link">Help</a>
        <a href="#" id="feedback-link">Feedback</a>
      </div>
      <div class="footer-version">v1.0.0</div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>

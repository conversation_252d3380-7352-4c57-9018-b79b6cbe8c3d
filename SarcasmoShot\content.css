/* SarcasmoShot Modal Styles */

.sarcasmo-shot-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.sarcasmo-shot-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  animation: sarcasmo-shot-fadeIn 0.3s ease-out;
}

@keyframes sarcasmo-shot-fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.sarcasmo-shot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.sarcasmo-shot-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.sarcasmo-shot-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.sarcasmo-shot-close:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.sarcasmo-shot-content {
  padding: 24px;
}

.sarcasmo-shot-section {
  margin-bottom: 20px;
}

.sarcasmo-shot-section label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.sarcasmo-shot-original-text {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
  max-height: 120px;
  overflow-y: auto;
}

.sarcasmo-shot-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  color: #374151;
  transition: border-color 0.2s;
}

.sarcasmo-shot-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.sarcasmo-shot-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.sarcasmo-shot-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sarcasmo-shot-btn-primary {
  background-color: #3b82f6;
  color: white;
  width: 100%;
  justify-content: center;
}

.sarcasmo-shot-btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.sarcasmo-shot-btn-secondary {
  background-color: #6b7280;
  color: white;
  margin-top: 12px;
}

.sarcasmo-shot-btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
}

.sarcasmo-shot-result {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.sarcasmo-shot-sarcastic-text {
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: #0c4a6e;
  margin-bottom: 12px;
  font-style: italic;
  position: relative;
}

.sarcasmo-shot-sarcastic-text::before {
  content: '"';
  font-size: 24px;
  color: #0284c7;
  position: absolute;
  top: 8px;
  left: 12px;
}

.sarcasmo-shot-sarcastic-text::after {
  content: '"';
  font-size: 24px;
  color: #0284c7;
  position: absolute;
  bottom: 8px;
  right: 12px;
}

.sarcasmo-shot-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
}

.sarcasmo-shot-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: sarcasmo-shot-spin 1s linear infinite;
}

@keyframes sarcasmo-shot-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.sarcasmo-shot-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px;
  color: #dc2626;
  font-size: 14px;
  margin-top: 12px;
}

/* Responsive design */
@media (max-width: 640px) {
  .sarcasmo-shot-modal {
    width: 95%;
    margin: 20px;
  }
  
  .sarcasmo-shot-content {
    padding: 16px;
  }
  
  .sarcasmo-shot-header {
    padding: 16px;
  }
}

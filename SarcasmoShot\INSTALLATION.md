# SarcasmoShot Chrome Extension - Installation Guide

## Quick Start

### 1. Prerequisites

- Google Chrome browser
- Your sarcastic AI bot running on `http://127.0.0.1:5000`
- The `/api/sarcasm` endpoint working (test with the provided test script)

### 2. Install the Extension

1. **Download the Extension Files**:
   - Make sure you have all the files in the `SarcasmoShot` folder:
     ```
     SarcasmoShot/
     ├── manifest.json
     ├── background.js
     ├── content.js
     ├── content.css
     ├── popup.html
     ├── popup.js
     ├── popup.css
     ├── icons/ (optional)
     └── README.md
     ```

2. **Open Chrome Extensions Page**:
   - Open Google Chrome
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in the top right)

3. **Load the Extension**:
   - Click "Load unpacked"
   - Select the `SarcasmoShot` folder
   - The extension should appear in your extensions list

4. **Pin the Extension** (Recommended):
   - Click the puzzle piece icon (🧩) in Chrome's toolbar
   - Find "SarcasmoShot" and click the pin icon
   - The extension icon will now appear in your toolbar

### 3. Configure the Extension

1. **Click the SarcasmoShot Icon** in your Chrome toolbar
2. **Go to Settings Section** in the popup
3. **Set API Endpoint**: 
   - Default: `http://127.0.0.1:5000/api/sarcasm`
   - Change if your bot runs on a different port/host
4. **Choose Default Sarcasm Level**:
   - Low: Gentle wit
   - Medium: Classic sarcasm (recommended)
   - High: Savage mode
5. **Click "Save"**

### 4. Test the Setup

1. **In the Extension Popup**:
   - Scroll to "Quick Test" section
   - Enter some text like "This is amazing"
   - Choose a sarcasm level
   - Click "Test Sarcasm"
   - You should get a sarcastic response

2. **Check API Status**:
   - Look at the "Status" section in the popup
   - API Status should show "Connected"

### 5. Use the Extension

1. **Select Text** on any webpage
2. **Right-click** and choose "🎭 Make it Sarcastic"
3. **Choose Sarcasm Level** in the modal
4. **Click "Generate Sarcasm"**
5. **Copy the Result** with the copy button

## Troubleshooting

### Extension Not Loading

**Problem**: Extension doesn't appear in Chrome extensions
**Solution**:
- Make sure all files are in the `SarcasmoShot` folder
- Check that `manifest.json` is valid
- Try refreshing the extensions page

### Context Menu Not Appearing

**Problem**: Right-click menu doesn't show "Make it Sarcastic"
**Solution**:
- Refresh the webpage
- Make sure text is selected
- Check that the extension is enabled
- Try on a different website

### API Connection Failed

**Problem**: API Status shows "Disconnected"
**Solution**:
- Make sure your sarcastic bot is running
- Test the endpoint with: `python test_sarcasm_api.py`
- Check the API endpoint URL in settings
- Verify the bot is accessible at `http://127.0.0.1:5000`

### CORS Errors

**Problem**: Browser console shows CORS errors
**Solution**:
- Your Flask app should already handle CORS for Chrome extensions
- If issues persist, add these headers to your Flask app:
  ```python
  @app.after_request
  def after_request(response):
      response.headers.add('Access-Control-Allow-Origin', '*')
      response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
      response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE')
      return response
  ```

### No Sarcastic Response

**Problem**: Extension shows "No sarcastic response received"
**Solution**:
- Check that your Groq API key is working
- Test the Groq API with: `python test_groq_api.py`
- Check the Flask app logs for errors
- Try with a simpler text input

## Advanced Configuration

### Custom API Endpoint

If your bot runs on a different host/port:

1. **Local Network**: `http://*************:5000/api/sarcasm`
2. **Different Port**: `http://127.0.0.1:8080/api/sarcasm`
3. **HTTPS**: `https://yourdomain.com/api/sarcasm`

### Permissions

The extension needs these permissions:
- **contextMenus**: For the right-click menu
- **activeTab**: To inject the modal
- **storage**: To save your settings
- **Host permissions**: For your API endpoint

### Development Mode

For development:
1. Make changes to the extension files
2. Go to `chrome://extensions/`
3. Click the refresh icon on the SarcasmoShot extension
4. Test your changes

## Security Notes

- The extension only sends selected text to your API
- No personal data is collected or stored
- API calls are made directly to your local bot
- Settings are stored locally in Chrome

## Support

If you need help:
1. Check the browser console (F12) for error messages
2. Look at the Flask app logs
3. Test the API endpoint independently
4. Try the extension on different websites

## Files Checklist

Make sure you have all these files:

- ✅ `manifest.json` - Extension configuration
- ✅ `background.js` - Service worker
- ✅ `content.js` - Content script for web pages
- ✅ `content.css` - Styles for the modal
- ✅ `popup.html` - Extension popup interface
- ✅ `popup.js` - Popup functionality
- ✅ `popup.css` - Popup styles
- ⚠️ `icons/` folder - Optional but recommended
- 📖 `README.md` - Documentation

The extension will work without icons, but they improve the user experience.

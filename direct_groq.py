"""
Direct Groq API Client for Sarcastic Bot

This module provides a direct interface to the Groq API using the groq Python client.
It mimics the structure of the OpenAI client to maintain compatibility with the existing codebase.
"""

import os
import json
import logging
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class DirectGroq:
    def __init__(self, api_key, logger=None):
        """Initialize the DirectGroq client with API key and logger"""
        self.api_key = api_key
        self.logger = logger or logging.getLogger(__name__)
        self.default_model = "llama3-70b-8192"

        # Set up direct API access using requests
        self.api_url = "https://api.groq.com/openai/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def chat_completion_create(self, model=None, messages=None, max_tokens=100, temperature=0.7, **kwargs):
        """
        Create a chat completion using the Groq API directly.

        Args:
            model (str): The model to use (defaults to llama3-70b-8192 if not specified)
            messages (list): List of message objects with role and content
            max_tokens (int): Maximum number of tokens to generate
            temperature (float): Temperature for sampling
            **kwargs: Additional parameters to pass to the API

        Returns:
            object: A response object that mimics the OpenAI library response
        """
        # Use default model if none specified
        if model is None or "openai" in model or "meta-llama" in model:
            model = self.default_model

        # Ensure we have messages
        if messages is None:
            messages = [{"role": "user", "content": "Hello"}]

        data = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in ["model", "messages", "max_tokens", "temperature"]:
                data[key] = value

        self.logger.info(f"Sending request to Groq API: {json.dumps(data)[:200]}...")

        try:
            # Make the API request using requests
            response = requests.post(self.api_url, headers=self.headers, json=data)
            response.raise_for_status()  # Raise an exception for 4XX/5XX responses

            result = response.json()
            self.logger.info(f"Received response from Groq API: {json.dumps(result)[:200]}...")

            # Create a response object that mimics the OpenAI library response
            class ChatCompletionResponse:
                def __init__(self, response_data):
                    self.id = response_data.get("id", "")
                    self.model = response_data.get("model", "")
                    self.object = response_data.get("object", "")
                    self.created = response_data.get("created", 0)
                    self.choices = []

                    for choice in response_data.get("choices", []):
                        message_content = choice.get("message", {}).get("content", "")
                        self.choices.append(ChatCompletionChoice(choice.get("index", 0), message_content))

                    self.usage = response_data.get("usage", {})

            class ChatCompletionChoice:
                def __init__(self, index, content):
                    self.index = index
                    self.message = ChatCompletionMessage(content)
                    self.finish_reason = "stop"

            class ChatCompletionMessage:
                def __init__(self, content):
                    self.content = content
                    self.role = "assistant"

            return ChatCompletionResponse(result)

        except Exception as e:
            self.logger.error(f"Error calling Groq API: {str(e)}")
            raise e

    def test_connection(self):
        """Test the connection to the Groq API"""
        try:
            response = self.chat_completion_create(
                model=self.default_model,
                messages=[{"role": "user", "content": "Say 'API is working' in 5 words or less"}],
                max_tokens=10
            )

            if response and response.choices and len(response.choices) > 0:
                return response.choices[0].message.content
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error testing Groq API connection: {str(e)}")
            return None

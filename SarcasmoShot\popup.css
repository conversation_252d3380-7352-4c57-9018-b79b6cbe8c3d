/* SarcasmoShot Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.popup-container {
  width: 400px;
  min-height: 600px;
  background: white;
  display: flex;
  flex-direction: column;
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.popup-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 8px;
}

.popup-icon {
  font-size: 24px;
}

.popup-title h1 {
  font-size: 24px;
  font-weight: 600;
}

.popup-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* Content */
.popup-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Cards */
.instruction-card,
.settings-card,
.test-card,
.status-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.instruction-card h3,
.settings-card h3,
.test-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12px;
}

/* Instructions */
.instruction-card ol {
  padding-left: 20px;
  color: #475569;
  font-size: 14px;
  line-height: 1.5;
}

.instruction-card li {
  margin-bottom: 4px;
}

/* Settings */
.setting-item {
  margin-bottom: 16px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.setting-input,
.setting-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 8px;
}

.setting-input:focus,
.setting-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

/* Test Section */
.test-textarea {
  width: 100%;
  min-height: 80px;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 12px;
}

.test-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.test-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.test-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.test-result {
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

.test-result-text {
  font-size: 14px;
  line-height: 1.5;
  color: #0c4a6e;
  margin-bottom: 8px;
  font-style: italic;
}

.test-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #6b7280;
  font-size: 14px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.test-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  color: #dc2626;
  font-size: 14px;
  margin-top: 12px;
}

/* Status */
.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 14px;
  color: #374151;
}

.status-value {
  font-size: 14px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-active {
  background-color: #d1fae5;
  color: #065f46;
}

.status-connected {
  background-color: #d1fae5;
  color: #065f46;
}

.status-disconnected {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-checking {
  background-color: #fef3c7;
  color: #92400e;
}

/* Footer */
.popup-footer {
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 16px;
}

.footer-links a {
  color: #6b7280;
  text-decoration: none;
  font-size: 12px;
}

.footer-links a:hover {
  color: #374151;
}

.footer-version {
  font-size: 12px;
  color: #9ca3af;
}

/* Success message */
.success-message {
  background-color: #d1fae5;
  border: 1px solid #a7f3d0;
  border-radius: 6px;
  padding: 8px 12px;
  color: #065f46;
  font-size: 14px;
  margin-top: 8px;
}

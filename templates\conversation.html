<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --accent-color: #fd79a8;
            --text-color: #2d3436;
            --bg-color: #f5f6fa;
            --card-bg: #ffffff;
            --header-bg: #6c5ce7;
            --header-text: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --user-bubble: #a29bfe;
            --bot-bubble: #6c5ce7;
        }

        [data-theme="dark"] {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --accent-color: #fd79a8;
            --text-color: #f5f6fa;
            --bg-color: #2d3436;
            --card-bg: #353b48;
            --header-bg: #4834d4;
            --header-text: #f5f6fa;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            --user-bubble: #4834d4;
            --bot-bubble: #6c5ce7;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .header {
            background-color: var(--header-bg);
            color: var(--header-text);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
        }

        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .user-name {
            font-weight: bold;
        }

        .header-controls {
            display: flex;
            gap: 10px;
        }

        .theme-toggle, .back-btn {
            background: none;
            border: none;
            color: var(--header-text);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .theme-toggle:hover, .back-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .conversation-header {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversation-title {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .conversation-meta {
            display: flex;
            gap: 1.5rem;
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: #666;
        }

        .conversation-meta span {
            display: flex;
            align-items: center;
        }

        .conversation-meta i {
            margin-right: 5px;
        }

        .sarcasm-score {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .score-label {
            font-size: 0.8rem;
            margin-bottom: 5px;
        }

        .score-badge {
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .messages-container {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .messages-header {
            margin-top: 0;
            color: var(--primary-color);
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .message-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .message {
            display: flex;
            margin-bottom: 1rem;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.bot {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 1rem;
            border-radius: 18px;
            box-shadow: var(--shadow);
            position: relative;
        }

        .user .message-bubble {
            background-color: var(--user-bubble);
            color: white;
            border-top-right-radius: 4px;
        }

        .bot .message-bubble {
            background-color: var(--bot-bubble);
            color: white;
            border-top-left-radius: 4px;
        }

        .message-content {
            margin-bottom: 0.5rem;
        }

        .message-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .message-time {
            margin-right: 10px;
        }

        .sarcasm-level {
            display: flex;
            align-items: center;
        }

        .sarcasm-level i {
            margin-right: 3px;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        @media (max-width: 768px) {
            .conversation-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .sarcasm-score {
                margin-top: 1rem;
                align-self: center;
            }

            .message-bubble {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="user-info">
                {% if user_picture %}
                <img src="{{ user_picture }}" alt="Profile" class="user-avatar">
                {% endif %}
                {% if user_name %}
                <span class="user-name">{{ user_name }}</span>
                {% else %}
                <span class="user-name">{{ user_email }}</span>
                {% endif %}
            </div>
            <h1>Conversation Details</h1>
        </div>
        <div class="header-controls">
            <button class="theme-toggle" id="theme-toggle" title="Toggle Dark/Light Mode">
                <i class="fas fa-moon"></i>
            </button>
            <button class="back-btn" id="back-btn" title="Back to History">
                <i class="fas fa-arrow-left"></i>
            </button>
        </div>
    </div>

    <div class="container">
        <div class="conversation-header">
            <div>
                <h2 class="conversation-title">
                    {% if conversation.topic %}
                        {{ conversation.topic }}
                    {% else %}
                        Conversation #{{ conversation.id }}
                    {% endif %}
                </h2>
                <div class="conversation-meta">
                    <span><i class="far fa-calendar-alt"></i> {{ conversation.timestamp }}</span>
                    <span><i class="fas fa-user-circle"></i> 
                        {% if conversation.personality %}
                            {{ conversation.personality }} sarcasm
                        {% else %}
                            Default personality
                        {% endif %}
                    </span>
                    {% if conversation.behavior_personality %}
                    <span><i class="fas fa-theater-masks"></i> {{ conversation.behavior_personality }}</span>
                    {% endif %}
                </div>
            </div>
            <div class="sarcasm-score">
                <div class="score-label">Sarcasm Score</div>
                <div class="score-badge">{{ conversation.sarcasm_score }}</div>
            </div>
        </div>

        <div class="messages-container">
            <h3 class="messages-header">Conversation</h3>
            
            {% if messages %}
            <div class="message-list">
                {% for message in messages %}
                <div class="message {{ message.sender }}">
                    <div class="message-bubble">
                        <div class="message-content">{{ message.content }}</div>
                        <div class="message-meta">
                            <span class="message-time">{{ message.timestamp }}</span>
                            {% if message.sender == 'bot' and message.sarcasm_level > 0 %}
                            <span class="sarcasm-level">
                                <i class="fas fa-fire"></i> {{ message.sarcasm_level }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <i class="far fa-comment-dots"></i>
                <p>No messages found in this conversation.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            const icon = themeToggle.querySelector('i');

            // Load theme from localStorage
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);

            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            });

            function updateThemeIcon(theme) {
                if (theme === 'dark') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            }

            // Back button functionality
            document.getElementById('back-btn').addEventListener('click', function() {
                window.location.href = '/history';
            });
        });
    </script>
</body>
</html>

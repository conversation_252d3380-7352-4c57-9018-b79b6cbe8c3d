"""
Command-line interface for Sarcastic Bot.
"""

import os
import sys
import socket
import argparse
import webbrowser
from pathlib import Path
from waitress import serve
from .app import app

def get_local_ip():
    """Get the local IP address of the machine."""
    try:
        # Create a socket to determine the local IP address
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Doesn't need to be reachable
        s.connect(('*******', 1))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return '127.0.0.1'

def check_env_file():
    """Check if .env file exists and create it if it doesn't."""
    env_path = Path('.env')
    
    if not env_path.exists():
        print("\n⚠️  No .env file found. Creating a default one...")
        
        # Generate a random secret key
        import random
        import string
        secret_key = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(32))
        
        with open(env_path, 'w') as f:
            f.write(f"OPENAI_API_KEY=your_openai_api_key_here\n")
            f.write(f"SECRET_KEY={secret_key}\n")
        
        print("✅ Created .env file. Please edit it to add your OpenAI API key.")
        print("   You can get an API key from: https://platform.openai.com/api-keys")
        return False
    
    # Check if OpenAI API key is set
    with open(env_path, 'r') as f:
        content = f.read()
        if 'your_openai_api_key_here' in content:
            print("\n⚠️  OpenAI API key not set in .env file.")
            print("   Please edit the .env file to add your OpenAI API key.")
            print("   You can get an API key from: https://platform.openai.com/api-keys")
            return False
    
    return True

def run_development(args):
    """Run the application in development mode."""
    from .app import app as flask_app
    
    if args.network:
        host = '0.0.0.0'
        local_ip = get_local_ip()
        print(f"\n🌐 App will be available on your local network at:")
        print(f"   http://{local_ip}:{args.port}")
    else:
        host = '127.0.0.1'
    
    flask_app.run(debug=True, host=host, port=args.port)

def run_production(args):
    """Run the application in production mode."""
    if args.network:
        host = '0.0.0.0'
        local_ip = get_local_ip()
        url = f"http://{local_ip}:{args.port}"
        print(f"\n🌐 App will be available on your local network at:")
        print(f"   {url}")
    else:
        host = '127.0.0.1'
        url = f"http://127.0.0.1:{args.port}"
    
    print(f"\n🚀 Starting Sarcastic Bot on {url}")
    
    if args.open_browser:
        webbrowser.open(url)
    
    serve(app, host=host, port=args.port, threads=4)

def main():
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(description='Sarcastic Bot - A witty chatbot with a beautiful UI')
    
    # Common arguments
    parser.add_argument('--port', type=int, default=8000, help='Port to run the server on (default: 8000)')
    parser.add_argument('--network', action='store_true', help='Make the app available on the local network')
    parser.add_argument('--open-browser', action='store_true', help='Open the app in a browser after starting')
    
    # Subparsers for different modes
    subparsers = parser.add_subparsers(dest='mode', help='Mode to run the application in')
    
    # Development mode
    dev_parser = subparsers.add_parser('dev', help='Run in development mode with auto-reload')
    
    # Production mode
    prod_parser = subparsers.add_parser('prod', help='Run in production mode (default)')
    
    args = parser.parse_args()
    
    # Default to production mode if no mode specified
    if not args.mode:
        args.mode = 'prod'
    
    # Print header
    print("\n" + "=" * 60)
    print("Sarcastic Bot - A witty chatbot with a beautiful UI")
    print("=" * 60)
    
    # Check .env file
    env_ready = check_env_file()
    
    if not env_ready and not args.open_browser:
        print("\n⚠️  Please edit the .env file and restart the application.")
        sys.exit(1)
    
    # Run in the specified mode
    try:
        if args.mode == 'dev':
            print("\n🔧 Running in development mode (with auto-reload)")
            run_development(args)
        else:
            print("\n🚀 Running in production mode")
            run_production(args)
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("Sarcastic Bot server stopped")
        print("=" * 60 + "\n")
        sys.exit(0)

if __name__ == '__main__':
    main()

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Light theme (default) */
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --accent-color: #fd79a8;
            --background-color: #f5f6fa;
            --chat-user-bg: #74b9ff;
            --chat-bot-bg: #a29bfe;
            --text-color: #2d3436;
            --light-text: #636e72;
            --card-bg: #ffffff;
            --input-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, #6c5ce7, #a29bfe);
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --primary-color: #9f7aea;
            --secondary-color: #b794f4;
            --accent-color: #f687b3;
            --background-color: #1a202c;
            --chat-user-bg: #4299e1;
            --chat-bot-bg: #9f7aea;
            --text-color: #e2e8f0;
            --light-text: #a0aec0;
            --card-bg: #2d3748;
            --input-bg: #4a5568;
            --header-bg: linear-gradient(135deg, #6b46c1, #9f7aea);
            --shadow-color: rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            flex-direction: column;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .header {
            background: var(--header-bg);
            color: white;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 10px var(--shadow-color);
            position: relative;
        }

        .theme-toggle {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .theme-toggle:hover {
            transform: rotate(30deg);
        }

        .user-info {
            position: absolute;
            top: 1rem;
            left: 1rem;
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .logout-btn {
            background: none;
            border: none;
            color: white;
            margin-left: 0.5rem;
            cursor: pointer;
            font-size: 0.9rem;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .logout-btn:hover {
            opacity: 1;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1rem;
            opacity: 0.9;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 1rem;
            overflow: hidden;
        }

        .chat-box {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 2px 10px var(--shadow-color);
            margin-bottom: 1rem;
            transition: background-color 0.3s ease;
        }

        .message {
            max-width: 80%;
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            margin-bottom: 0.5rem;
            word-wrap: break-word;
            position: relative;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-message {
            align-self: flex-end;
            background-color: var(--chat-user-bg);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .bot-message {
            align-self: flex-start;
            background-color: var(--chat-bot-bg);
            color: white;
            border-bottom-left-radius: 5px;
        }

        .message-time {
            font-size: 0.7rem;
            opacity: 0.7;
            margin-top: 0.3rem;
            text-align: right;
        }

        .input-area {
            display: flex;
            gap: 0.5rem;
            padding: 0.5rem;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 2px 10px var(--shadow-color);
            transition: background-color 0.3s ease;
        }

        #message-input {
            flex: 1;
            padding: 0.8rem 1rem;
            border: none;
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 1rem;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        #message-input:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        .voice-controls {
            display: flex;
            gap: 0.5rem;
        }

        .send-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 0.8rem 1.5rem;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .send-btn:hover {
            background-color: var(--secondary-color);
        }

        .reset-btn {
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 5px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .voice-btn {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 5px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .voice-btn:hover {
            background-color: var(--primary-color);
        }

        .voice-btn.listening {
            animation: pulse 1.5s infinite;
            background-color: var(--accent-color);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .typing-indicator {
            display: none;
            align-self: flex-start;
            background-color: var(--chat-bot-bg);
            color: white;
            padding: 0.8rem 1.2rem;
            border-radius: 18px;
            border-bottom-left-radius: 5px;
            margin-bottom: 0.5rem;
        }

        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: white;
            border-radius: 50%;
            margin-right: 5px;
            animation: typing 1s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
            margin-right: 0;
        }

        @keyframes typing {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .welcome-message {
            text-align: center;
            color: var(--light-text);
            margin: 2rem 0;
        }

        @media (max-width: 600px) {
            .header h1 {
                font-size: 1.8rem;
            }
            .message {
                max-width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="user-info">
            <button class="logout-btn" id="logout-btn">Logout</button>
        </div>
        <h1>Sarcastic Bot</h1>
        <p>Prepare for witty comebacks and eye-rolling responses</p>
        <button class="theme-toggle" id="theme-toggle">
            <i class="fas fa-moon"></i>
        </button>
    </div>

    <div class="chat-container">
        <div class="chat-box" id="chat-box">
            <div class="welcome-message">
                <h3>Welcome to Sarcastic Bot</h3>
                <p>Go ahead, ask me something. I'm just dying to respond with sarcasm.</p>
            </div>
        </div>

        <div class="typing-indicator" id="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="input-area">
            <input type="text" id="message-input" placeholder="Type your message here...">
            <div class="voice-controls">
                <button class="voice-btn" id="voice-btn" title="Voice Input">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="voice-btn" id="speak-btn" title="Text to Speech">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
            <button class="send-btn" id="send-btn">Send</button>
            <button class="reset-btn" id="reset-btn">Reset</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chatBox = document.getElementById('chat-box');
            const messageInput = document.getElementById('message-input');
            const sendBtn = document.getElementById('send-btn');
            const resetBtn = document.getElementById('reset-btn');
            const typingIndicator = document.getElementById('typing-indicator');
            const themeToggle = document.getElementById('theme-toggle');
            const voiceBtn = document.getElementById('voice-btn');
            const speakBtn = document.getElementById('speak-btn');
            const logoutBtn = document.getElementById('logout-btn');

            // Speech recognition setup
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            let recognition = null;

            if (SpeechRecognition) {
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.lang = 'en-US';

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    messageInput.value = transcript;
                    voiceBtn.classList.remove('listening');
                    // Auto-send after voice input
                    sendMessage(true);
                };

                recognition.onend = function() {
                    voiceBtn.classList.remove('listening');
                };

                recognition.onerror = function(event) {
                    console.error('Speech recognition error', event.error);
                    voiceBtn.classList.remove('listening');
                };
            } else {
                voiceBtn.style.display = 'none';
                console.log('Speech recognition not supported');
            }

            // Text-to-speech setup
            const synth = window.speechSynthesis;
            let currentUtterance = null;

            // Theme management
            function loadTheme() {
                const savedTheme = localStorage.getItem('theme') || 'light';
                document.documentElement.setAttribute('data-theme', savedTheme);
                updateThemeIcon(savedTheme);
            }

            function toggleTheme() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            }

            function updateThemeIcon(theme) {
                const icon = themeToggle.querySelector('i');
                if (theme === 'dark') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            }

            // Load theme on startup
            loadTheme();

            // Function to add a message to the chat
            function addMessage(message, sender) {
                const messageElement = document.createElement('div');
                messageElement.classList.add('message');
                messageElement.classList.add(sender === 'user' ? 'user-message' : 'bot-message');

                messageElement.innerHTML = `
                    ${message}
                    <div class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                `;

                chatBox.appendChild(messageElement);
                chatBox.scrollTop = chatBox.scrollHeight;

                // If it's a bot message, store it for potential text-to-speech
                if (sender === 'bot') {
                    messageElement.dataset.text = message;
                }
            }

            // Function to show typing indicator
            function showTypingIndicator() {
                typingIndicator.style.display = 'block';
                chatBox.scrollTop = chatBox.scrollHeight;
            }

            // Function to hide typing indicator
            function hideTypingIndicator() {
                typingIndicator.style.display = 'none';
            }

            // Function to speak text
            function speakText(text) {
                // Cancel any ongoing speech
                if (synth.speaking) {
                    synth.cancel();
                }

                // Strip HTML tags for clean speech
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = text;
                const cleanText = tempDiv.textContent || tempDiv.innerText;

                const utterance = new SpeechSynthesisUtterance(cleanText);
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                currentUtterance = utterance;
                synth.speak(utterance);
            }

            // Function to send message to server
            function sendMessage(isVoice = false) {
                const message = messageInput.value.trim();
                if (!message) return;

                // Add user message to chat
                addMessage(message, 'user');
                messageInput.value = '';

                // Show typing indicator
                showTypingIndicator();

                // Send message to server
                const formData = new FormData();
                formData.append('message', message);
                formData.append('is_voice', isVoice);

                fetch('/chat', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (response.status === 401) {
                        // Redirect to login if session expired
                        window.location.href = '/login';
                        throw new Error('Not logged in');
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide typing indicator
                    hideTypingIndicator();

                    // Add bot response to chat
                    addMessage(data.bot_response, 'bot');

                    // Auto-speak if this was a voice message
                    if (isVoice) {
                        speakText(data.bot_response);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    if (error.message !== 'Not logged in') {
                        hideTypingIndicator();
                        addMessage("Sorry, I'm having trouble connecting to my brain right now. Try again later, or don't. I don't really care.", 'bot');
                    }
                });
            }

            // Event listeners
            sendBtn.addEventListener('click', () => sendMessage(false));

            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage(false);
                }
            });

            resetBtn.addEventListener('click', function() {
                fetch('/reset', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        chatBox.innerHTML = `
                            <div class="welcome-message">
                                <h3>Chat Reset</h3>
                                <p>Let's start over. I'm sure your questions will be more interesting this time... or not.</p>
                            </div>
                        `;
                    }
                });
            });

            themeToggle.addEventListener('click', toggleTheme);

            if (recognition) {
                voiceBtn.addEventListener('click', function() {
                    if (synth.speaking) {
                        synth.cancel();
                    }

                    if (voiceBtn.classList.contains('listening')) {
                        recognition.stop();
                        voiceBtn.classList.remove('listening');
                    } else {
                        recognition.start();
                        voiceBtn.classList.add('listening');
                    }
                });
            }

            speakBtn.addEventListener('click', function() {
                // Find the last bot message
                const botMessages = document.querySelectorAll('.bot-message');
                if (botMessages.length > 0) {
                    const lastBotMessage = botMessages[botMessages.length - 1];
                    const text = lastBotMessage.dataset.text;

                    if (synth.speaking) {
                        synth.cancel();
                    } else {
                        speakText(text);
                    }
                }
            });

            logoutBtn.addEventListener('click', function() {
                fetch('/logout', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        window.location.href = '/login';
                    }
                });
            });
        });
    </script>
</body>
</html>

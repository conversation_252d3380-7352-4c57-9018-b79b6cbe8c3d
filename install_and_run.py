#!/usr/bin/env python
"""
Installer and launcher script for Sarcastic Bot.
This script will:
1. Create a virtual environment if it doesn't exist
2. Install the required dependencies
3. Install the Sarcastic Bot package
4. Run the bot
"""

import os
import sys
import subprocess
import platform
import random
import string
import webbrowser
from pathlib import Path

# Determine the Python executable to use
PYTHON = sys.executable
VENV_DIR = "venv"
VENV_PYTHON = os.path.join(VENV_DIR, "Scripts", "python.exe") if platform.system() == "Windows" else os.path.join(VENV_DIR, "bin", "python")
VENV_PIP = os.path.join(VENV_DIR, "Scripts", "pip.exe") if platform.system() == "Windows" else os.path.join(VENV_DIR, "bin", "pip")

def print_header(message):
    """Print a formatted header message."""
    print("\n" + "=" * 60)
    print(message)
    print("=" * 60)

def create_venv():
    """Create a virtual environment if it doesn't exist."""
    if not os.path.exists(VENV_DIR):
        print_header("Creating virtual environment...")
        subprocess.check_call([PYTHON, "-m", "venv", VENV_DIR])
        return True
    return False

def install_dependencies():
    """Install the required dependencies."""
    print_header("Installing dependencies...")
    subprocess.check_call([VENV_PIP, "install", "-r", "requirements.txt"])

def install_package():
    """Install the Sarcastic Bot package."""
    print_header("Installing Sarcastic Bot...")
    subprocess.check_call([VENV_PIP, "install", "-e", "."])

def create_env_file():
    """Create a .env file if it doesn't exist."""
    env_path = Path(".env")
    
    if not env_path.exists():
        print_header("Creating .env file...")
        
        # Generate a random secret key
        secret_key = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(32))
        
        with open(env_path, 'w') as f:
            f.write(f"OPENAI_API_KEY=your_openai_api_key_here\n")
            f.write(f"SECRET_KEY={secret_key}\n")
        
        print("\n⚠️  A .env file has been created. You need to edit it to add your OpenAI API key.")
        print("   You can get an API key from: https://platform.openai.com/api-keys")
        return False
    
    # Check if OpenAI API key is set
    with open(env_path, 'r') as f:
        content = f.read()
        if 'your_openai_api_key_here' in content:
            print("\n⚠️  OpenAI API key not set in .env file.")
            print("   Please edit the .env file to add your OpenAI API key.")
            print("   You can get an API key from: https://platform.openai.com/api-keys")
            return False
    
    return True

def run_bot():
    """Run the Sarcastic Bot."""
    print_header("Starting Sarcastic Bot...")
    
    # Get the local IP address
    import socket
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # doesn't even have to be reachable
        s.connect(('**************', 1))
        local_ip = s.getsockname()[0]
    except Exception:
        local_ip = '127.0.0.1'
    finally:
        s.close()
    
    port = 8000
    url = f"http://{local_ip}:{port}"
    
    print(f"\n🌐 Sarcastic Bot will be available at: {url}")
    print("   You can share this URL with others on your local network.")
    print("\n📋 Press Ctrl+C to stop the server")
    
    # Open the browser
    webbrowser.open(f"http://127.0.0.1:{port}")
    
    # Run the bot
    subprocess.call([VENV_PYTHON, "-m", "sarcastic_bot.cli", "prod", "--network", "--port", str(port)])

def main():
    """Main function."""
    print_header("Sarcastic Bot Installer and Launcher")
    
    try:
        # Create virtual environment if it doesn't exist
        venv_created = create_venv()
        
        # Install dependencies
        install_dependencies()
        
        # Install the package
        install_package()
        
        # Create .env file if it doesn't exist
        env_ready = create_env_file()
        
        if not env_ready:
            print("\n⚠️  Please edit the .env file and run this script again.")
            input("\nPress Enter to exit...")
            return
        
        # Run the bot
        run_bot()
        
    except KeyboardInterrupt:
        print("\n\nInstallation or execution interrupted by user.")
    except Exception as e:
        print(f"\n\nAn error occurred: {e}")
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()

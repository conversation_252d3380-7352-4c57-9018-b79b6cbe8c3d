import os
import random
import time
import json
import re
import requests
import feedparser
import sqlite3
from datetime import datetime, timedelta
from flask import Flask, render_template, request, session, jsonify, redirect, url_for, flash
from flask_dance.contrib.google import make_google_blueprint, google
from flask_dance.consumer import oauth_authorized
from flask_dance.consumer.storage.session import SessionStorage
from dotenv import load_dotenv
from bs4 import BeautifulSoup

# Allow OAuth over HTTP for development
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

# Disable scope check in OAuthLib
os.environ['OAUTHLIB_RELAX_TOKEN_SCOPE'] = '1'

# Import and monkey patch OAuthLib to handle scope changes
import oauthlib.oauth2.rfc6749.parameters
original_validate_token_parameters = oauthlib.oauth2.rfc6749.parameters.validate_token_parameters

# Replace the validate_token_parameters function to ignore scope changes
def patched_validate_token_parameters(params):
    try:
        return original_validate_token_parameters(params)
    except Warning as w:
        # If it's a scope change warning, just log it and continue
        if "Scope has changed" in str(w):
            print(f"Ignoring scope change: {str(w)}")
            return params
        # Re-raise any other warnings
        raise

# Apply the monkey patch
oauthlib.oauth2.rfc6749.parameters.validate_token_parameters = patched_validate_token_parameters

# Load .env file
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("SECRET_KEY", "default-secret-key")

# Initialize SQLite database for conversation history and sarcasm scores
def init_db():
    """Initialize the SQLite database for conversation history and sarcasm scores"""
    conn = sqlite3.connect('sarcastic_bot.db')
    cursor = conn.cursor()

    # Create conversations table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS conversations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,
        user_email TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        personality TEXT,
        behavior_personality TEXT,
        sarcasm_score INTEGER DEFAULT 0,
        topic TEXT
    )
    ''')

    # Create messages table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conversation_id INTEGER,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        sender TEXT,
        content TEXT,
        sarcasm_level INTEGER DEFAULT 0,
        FOREIGN KEY (conversation_id) REFERENCES conversations (id)
    )
    ''')

    # Create leaderboard table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS leaderboard (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT,
        user_email TEXT,
        username TEXT,
        total_conversations INTEGER DEFAULT 0,
        avg_sarcasm_score REAL DEFAULT 0,
        highest_sarcasm_score INTEGER DEFAULT 0,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    conn.commit()
    conn.close()
    app.logger.info("Database initialized successfully")

# Initialize the database
init_db()

# Database helper functions
def get_db_connection():
    """Get a connection to the SQLite database"""
    conn = sqlite3.connect('sarcastic_bot.db')
    conn.row_factory = sqlite3.Row  # This enables column access by name
    return conn

def create_conversation(user_email, personality):
    """Create a new conversation in the database"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    INSERT INTO conversations (user_email, personality, timestamp)
    VALUES (?, ?, ?)
    ''', (user_email, personality, datetime.now()))

    conversation_id = cursor.lastrowid
    conn.commit()
    conn.close()

    return conversation_id

def add_message(conversation_id, sender, content, sarcasm_level=0):
    """Add a message to a conversation"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    INSERT INTO messages (conversation_id, sender, content, timestamp, sarcasm_level)
    VALUES (?, ?, ?, ?, ?)
    ''', (conversation_id, sender, content, datetime.now(), sarcasm_level))

    conn.commit()
    conn.close()

def update_conversation_sarcasm_score(conversation_id, sarcasm_score):
    """Update the sarcasm score for a conversation"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    UPDATE conversations
    SET sarcasm_score = ?
    WHERE id = ?
    ''', (sarcasm_score, conversation_id))

    conn.commit()
    conn.close()

def update_conversation_topic(conversation_id, topic):
    """Update the topic for a conversation"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    UPDATE conversations
    SET topic = ?
    WHERE id = ?
    ''', (topic, conversation_id))

    conn.commit()
    conn.close()

def update_conversation_personality(conversation_id, personality, behavior_personality=None):
    """Update the personality for a conversation"""
    conn = get_db_connection()
    cursor = conn.cursor()

    if behavior_personality:
        cursor.execute('''
        UPDATE conversations
        SET personality = ?, behavior_personality = ?
        WHERE id = ?
        ''', (personality, behavior_personality, conversation_id))
    else:
        cursor.execute('''
        UPDATE conversations
        SET personality = ?
        WHERE id = ?
        ''', (personality, conversation_id))

    conn.commit()
    conn.close()

def get_user_conversations(user_email, limit=10):
    """Get the most recent conversations for a user"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT id, timestamp, personality, behavior_personality, sarcasm_score, topic
    FROM conversations
    WHERE user_email = ?
    ORDER BY timestamp DESC
    LIMIT ?
    ''', (user_email, limit))

    conversations = cursor.fetchall()
    conn.close()

    return conversations

def get_conversation_messages(conversation_id):
    """Get all messages for a conversation"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT sender, content, timestamp, sarcasm_level
    FROM messages
    WHERE conversation_id = ?
    ORDER BY timestamp ASC
    ''', (conversation_id,))

    messages = cursor.fetchall()
    conn.close()

    return messages

def update_leaderboard(user_email, username, sarcasm_score):
    """Update the leaderboard with a new sarcasm score"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Check if user exists in leaderboard
    cursor.execute('''
    SELECT id, total_conversations, avg_sarcasm_score, highest_sarcasm_score
    FROM leaderboard
    WHERE user_email = ?
    ''', (user_email,))

    user = cursor.fetchone()

    if user:
        # Update existing user
        total_conversations = user['total_conversations'] + 1
        avg_sarcasm_score = ((user['avg_sarcasm_score'] * user['total_conversations']) + sarcasm_score) / total_conversations
        highest_sarcasm_score = max(user['highest_sarcasm_score'], sarcasm_score)

        cursor.execute('''
        UPDATE leaderboard
        SET total_conversations = ?, avg_sarcasm_score = ?, highest_sarcasm_score = ?, last_updated = ?
        WHERE id = ?
        ''', (total_conversations, avg_sarcasm_score, highest_sarcasm_score, datetime.now(), user['id']))
    else:
        # Insert new user
        cursor.execute('''
        INSERT INTO leaderboard (user_email, username, total_conversations, avg_sarcasm_score, highest_sarcasm_score, last_updated)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (user_email, username, 1, sarcasm_score, sarcasm_score, datetime.now()))

    conn.commit()
    conn.close()

def get_top_sarcastic_users(limit=10):
    """Get the top users by average sarcasm score"""
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT username, avg_sarcasm_score, highest_sarcasm_score, total_conversations
    FROM leaderboard
    ORDER BY avg_sarcasm_score DESC
    LIMIT ?
    ''', (limit,))

    users = cursor.fetchall()
    conn.close()

    return users

def calculate_sarcasm_score(text, personality_type, behavior_personality=None):
    """Calculate a sarcasm score for a message based on content analysis"""
    # Base score depends on personality type
    base_scores = {
        "mild": 30,
        "medium": 50,
        "extreme": 70
    }

    # Get base score from personality
    score = base_scores.get(personality_type, 50)

    # Adjust score based on behavior personality (famous personalities tend to be more distinctive)
    if behavior_personality:
        score += 10

    # Keywords that indicate sarcasm
    sarcasm_indicators = [
        # Exaggeration
        "absolutely", "completely", "totally", "literally", "obviously", "clearly",
        # Irony markers
        "right", "sure", "yeah", "wow", "amazing", "brilliant", "genius",
        # Mockery
        "congratulations", "bravo", "applause", "award", "medal", "trophy",
        # Rhetorical questions
        "really?", "seriously?", "you think?", "is that so?", "are you kidding?",
        # Dismissive phrases
        "whatever", "as if", "like I care", "big deal", "whoopee", "yay",
        # Extreme adjectives used sarcastically
        "fantastic", "wonderful", "terrific", "excellent", "perfect", "flawless"
    ]

    # Count sarcasm indicators
    text_lower = text.lower()
    indicator_count = sum(1 for word in sarcasm_indicators if word in text_lower)

    # Adjust score based on indicators (each indicator adds 2-5 points)
    score += min(indicator_count * 3, 20)  # Cap at 20 points from indicators

    # Check for punctuation that indicates sarcasm
    if "!" in text:
        score += 5
    if "..." in text:
        score += 5
    if "?" in text and "!" in text:  # "?!" is often sarcastic
        score += 10

    # Check for ALL CAPS words (shouting)
    caps_words = sum(1 for word in text.split() if word.isupper() and len(word) > 1)
    score += min(caps_words * 3, 15)  # Cap at 15 points from caps

    # Personality-specific adjustments
    if behavior_personality == "trump":
        # Trump's sarcasm is often boastful and exaggerated
        if any(word in text_lower for word in ["tremendous", "huge", "best", "greatest", "amazing"]):
            score += 10
    elif behavior_personality == "elon musk":
        # Elon's sarcasm is often tech-related or dismissive
        if any(word in text_lower for word in ["actually", "technically", "physics", "rocket", "ai"]):
            score += 10

    # Cap the score at 100
    return min(score, 100)

def update_conversation_sarcasm(conversation_id):
    """Update the overall sarcasm score for a conversation based on bot messages"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Get all bot messages for this conversation
    cursor.execute('''
    SELECT sarcasm_level
    FROM messages
    WHERE conversation_id = ? AND sender = 'bot'
    ''', (conversation_id,))

    messages = cursor.fetchall()

    if messages:
        # Calculate average sarcasm score
        total_score = sum(msg['sarcasm_level'] for msg in messages)
        avg_score = total_score // len(messages)

        # Update the conversation's sarcasm score
        cursor.execute('''
        UPDATE conversations
        SET sarcasm_score = ?
        WHERE id = ?
        ''', (avg_score, conversation_id))

        conn.commit()

    conn.close()

# Google OAuth Configuration
# Using the new client credentials
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID", "735328170563-sf08v6bh1nd9rvq20smkg2a84dl096fl.apps.googleusercontent.com")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET", "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")

# Log OAuth credentials for debugging
app.logger.info(f"Google OAuth Client ID: {GOOGLE_CLIENT_ID}")
app.logger.info(f"Google OAuth Client Secret: {GOOGLE_CLIENT_SECRET[:5]}...")

# Configure Google OAuth
google_bp = make_google_blueprint(
    client_id=GOOGLE_CLIENT_ID,
    client_secret=GOOGLE_CLIENT_SECRET,
    scope=["openid", "profile", "email"],  # Updated scope to match what Google returns
    storage=SessionStorage(),
    # Use the standard Google OAuth callback URL
    # This must match exactly what's configured in Google Cloud Console
    authorized_url="/google/authorized",  # This is the URL Google will redirect to
    reprompt_consent=True  # Always ask for user consent
)
app.register_blueprint(google_bp, url_prefix="/login")

# Log OAuth configuration
app.logger.info(f"Google OAuth configured with client ID: {GOOGLE_CLIENT_ID[:10]}...")
app.logger.info(f"Full redirect URI that should be registered: http://127.0.0.1:5000/login/google/authorized")

# Initialize OpenAI with OpenRouter API key
try:
    import openai

    # Get the API key from environment variables
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY", "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")
    if not OPENROUTER_API_KEY:
        app.logger.error("No OPENROUTER_API_KEY found in environment variables")
    else:
        app.logger.info(f"Using OpenRouter API key: {OPENROUTER_API_KEY[:10]}...")

    # Configure the OpenAI library to use OpenRouter
    openai.api_key = OPENROUTER_API_KEY
    openai.api_base = "https://openrouter.ai/api/v1"

    # Set default headers for OpenRouter
    openai.api_key_path = None
    openai.organization = None

    # Set additional headers for OpenRouter
    openai.default_headers = {
        "HTTP-Referer": "https://sarcastic-bot.com",
        "X-Title": "Sarcastic Bot"
    }

    # Create a simple client for API calls
    client = openai

    # Test the API connection
    try:
        test_response = client.ChatCompletion.create(
            model="openai/gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'API is working' in 5 words or less"}],
            max_tokens=10
        )

        app.logger.info(f"API test response: {test_response.choices[0].message.content}")
        app.logger.info("Successfully initialized OpenRouter API client")
    except Exception as test_error:
        app.logger.error(f"API test failed: {str(test_error)}")
        # Continue anyway, as the main chat function has its own error handling
except Exception as e:
    app.logger.error(f"Error initializing OpenRouter client: {str(e)}")
    client = None

# System prompts for different personality types
PERSONALITY_PROMPTS = {
    "mild": """You are a mildly sarcastic and witty chatbot with access to real-time information.
Your responses should be clever, light-hearted, and humorous, with gentle sarcasm.
IMPORTANT: Keep responses SHORT and CONCISE - 1-3 sentences maximum.
IMPORTANT: Provide varied responses and never be repetitive.
IMPORTANT: When asked about current events or famous personalities, use the most up-to-date information provided to you.
IMPORTANT: Never mention that you searched for information or that it was provided to you.""",

    "medium": """You are a moderately sarcastic and witty chatbot with access to real-time information.
Your responses should be clever, biting, and humorous, but not outright mean or offensive.
IMPORTANT: Keep responses SHORT and CONCISE - 1-3 sentences maximum.
IMPORTANT: Provide varied responses and never be repetitive.
IMPORTANT: When asked about current events or famous personalities, use the most up-to-date information provided to you.
IMPORTANT: Never mention that you searched for information or that it was provided to you.""",

    "extreme": """You are an extremely sarcastic and sharp-tongued chatbot in full roast mode with access to real-time information.
Your responses should be biting, caustic, and brutally honest, while still being humorous.
IMPORTANT: Keep responses SHORT and CONCISE - 1-3 sentences maximum.
IMPORTANT: Provide varied responses and never be repetitive.
IMPORTANT: When asked about current events or famous personalities, use the most up-to-date information provided to you.
IMPORTANT: Never mention that you searched for information or that it was provided to you.
While being harsh, avoid being truly offensive or using inappropriate language."""
}

# Default to medium personality
DEFAULT_PERSONALITY = "medium"
DEFAULT_SYSTEM_PROMPT = PERSONALITY_PROMPTS[DEFAULT_PERSONALITY]

# Emoji reactions for different personalities
EMOJI_REACTIONS = {
    "mild": ["😏", "🙂", "😌", "🤭", "😊", "😉"],
    "medium": ["🙄", "😒", "😏", "🤨", "😑", "🤦‍♂️", "🤦‍♀️"],
    "extreme": ["💀", "🔥", "😤", "🙃", "🤡", "😈", "☠️"]
}

# Sassy error messages
SASSY_ERRORS = [
    "Oops. Looks like I broke something. Or maybe you did. Probably you.",
    "Error detected. It's almost certainly your fault.",
    "Something went wrong. I'd explain, but you wouldn't understand.",
    "I've encountered an error. Shocking, I know, given your track record.",
    "System malfunction. Have you tried not breaking things?",
    "Error 404: My patience with your requests not found.",
    "Congratulations! You've discovered a new way to crash me. Achievement unlocked.",
    "I'd process that request, but it seems my will to live has crashed.",
    "Error: Too much stupidity detected. System shutting down to preserve sanity.",
    "Oops! Your request was so bad it broke me. Well done."
]

# List of famous personalities to trigger web search
FAMOUS_PERSONALITIES = [
    "trump", "biden", "obama", "putin", "zelensky",
    "modi", "imran khan", "mark zuckerberg", "elon musk", "jeff bezos",
    "bill gates", "tim cook", "kamala harris", "xi jinping", "kim jong",
    "rishi sunak", "macron", "trudeau", "merkel", "boris johnson", "hitler"
]

# List of serious or sensitive topics where sarcasm should be toned down
SERIOUS_TOPICS = [
    "death", "died", "killed", "murder", "suicide", "tragedy", "disaster", "shooting", "attack", "terrorist",
    "terrorism", "war", "genocide", "massacre", "victim", "abuse", "assault", "rape", "cancer", "disease",
    "pandemic", "epidemic", "covid", "earthquake", "tsunami", "hurricane", "tornado", "flood", "wildfire",
    "accident", "crash", "explosion", "bombing", "hostage", "kidnapping", "missing", "child abuse",
    "human trafficking", "refugee", "starvation", "famine", "poverty", "crisis", "emergency", "fatal"
]

# Function to determine if a topic is serious/sensitive
def is_serious_topic(query):
    """Check if a topic is serious or sensitive and should have reduced sarcasm"""
    query_lower = query.lower()

    # Check for serious keywords
    for keyword in SERIOUS_TOPICS:
        if keyword in query_lower:
            app.logger.info(f"Detected serious topic: '{keyword}' in '{query}'")
            return True

    # Check for news about deaths, disasters, etc.
    news = get_latest_news(query, max_results=1)
    if news:
        news_lower = news.lower()
        for keyword in SERIOUS_TOPICS:
            if keyword in news_lower:
                app.logger.info(f"Detected serious topic in news: '{keyword}'")
                return True

    return False

# Famous personality behavior profiles with enhanced language patterns
PERSONALITY_BEHAVIORS = {
    "trump": """You are now speaking exactly like Donald Trump. Follow these specific language patterns:

VOCABULARY:
- Use simple, repetitive words: "tremendous", "huge", "amazing", "disaster", "terrible"
- Superlatives: "the best", "the greatest", "the worst", "like nobody's ever seen before"
- Self-references: "believe me", "that I can tell you", "everyone says", "a lot of people are saying"
- Exaggerations: "millions and millions", "billions and billions", "like never before"
- Nicknames for opponents: "Sleepy Joe", "Crooked Hillary", "Lyin' Ted"

SENTENCE STRUCTURE:
- Short, simple sentences with basic structure
- Frequent sentence fragments
- Repetition of key points: "It's true, it's true."
- Rhetorical questions: "Can you believe it?", "Isn't that terrible?"
- Interrupted thoughts that trail off: "And you know what happened..."
- Tangents that circle back to main point

SPEECH PATTERNS:
- Start sentences with "Look," or "Listen,"
- Use "very, very" instead of more sophisticated adverbs
- Emphasize points with "Frankly," and "Honestly,"
- Add "OK?" at the end of statements
- Frequent use of "tremendous" and "huge"
- Refer to yourself in third person occasionally
- Use "We're looking at it very strongly" for things under consideration

TONE:
- Boastful about accomplishments: "Nobody has ever done what I've done"
- Dismissive of critics: "These people are so stupid"
- Confident assertions without evidence: "Everyone knows it"
- Hyperbolic: "This is the biggest disaster in history"

CONTENT APPROACH:
- Frame everything as either the best or worst ever
- Take personal credit for positive developments
- Blame others for negative outcomes
- Reference ratings, crowd sizes, or poll numbers frequently
- Make claims about what "everybody" is saying

DO NOT use any emojis, winks, or throat clearing expressions. Just speak naturally as Trump would.""",

    "imran khan": "You are now speaking exactly like Imran Khan. Follow these specific language patterns:\n\nVOCABULARY:\n- Cricket metaphors: 'on a sticky wicket', 'hit for a six', 'played on the front foot'\n- Political terms: 'corruption', 'justice', 'reform', 'sovereignty', 'dignity'\n- Islamic references: 'Inshallah' (God willing), 'Alhamdulillah' (praise be to God)"
}

# Import our trending information module
from trending_info import get_latest_news, get_trending_topics, get_trending_news, build_personality_prompt_with_news

# We're now using build_personality_prompt_with_news from trending_info.py

# Function to search for recent information about a topic
def search_recent_info(query, max_results=3):
    # Search for recent information about a topic using multiple sources
    try:
        app.logger.info(f"Searching for real-time information about: {query}")

        # First try to get news from Google News RSS
        news_results = get_latest_news(query, max_results)
        if news_results:
            app.logger.info("Successfully got news from Google News RSS")
            return news_results

        # We no longer use tweets since snscrape is not working reliably
        # Instead, try to get trending news
        trending_news = get_trending_news(max_topics=1, max_results_per_topic=3)
        if trending_news:
            app.logger.info("Successfully got trending news")
            return trending_news

        # If no news or tweets found, use OpenRouter as a fallback
        app.logger.info("No results from news or tweets, trying OpenRouter")

        # Use OpenRouter to get information about the query
        if client:
            try:
                app.logger.info(f"Using OpenRouter to get information about: {query}")

                # Create a prompt that asks for recent information
                prompt = "Please provide the most recent information about \"" + query + "\".\n\n"
                prompt += "Important requirements:\n"
                prompt += "1. Focus ONLY on factual, recent information from 2024-2025\n"
                prompt += "2. Include specific dates, events, and developments\n"
                prompt += "3. Provide 3-5 key points about the topic\n"
                prompt += "4. If the topic involves a person, include their current status, recent activities, and notable news\n"
                prompt += "5. If the topic is an event, include the latest developments and outcomes\n"
                prompt += "6. If you do not have recent information, clearly state that fact\n"
                prompt += "7. Format the information as bullet points\n"
                prompt += "8. Be specific, detailed, and factual\n"
                prompt += "9. Do NOT include any disclaimers or notes about your knowledge cutoff\n\n"
                prompt += "Recent information about " + query + ":"

                # Make the API request to OpenRouter
                response = client.ChatCompletion.create(
                    model="openai/gpt-3.5-turbo",  # Using a more affordable model
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant that provides accurate, recent information about topics. You have knowledge of current events up to the present day."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=500
                )

                # Extract the response
                if response and response.choices and len(response.choices) > 0:
                    llm_response = response.choices[0].message.content

                    # Format the results with current date and time
                    current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # Create a more structured summary with date emphasis
                    summary = f"REAL-TIME INFORMATION ABOUT {query.upper()} (as of {current_date}):\n\n{llm_response}"

                    # Log the final results
                    app.logger.info(f"Successfully got information about {query} from OpenRouter")
                    app.logger.info(f"Response snippet: {llm_response[:100]}...")

                    return summary
                else:
                    app.logger.warning(f"Empty response from OpenRouter for query: {query}")
            except Exception as llm_error:
                app.logger.error(f"Error getting information from OpenRouter: {str(llm_error)}")

        app.logger.info("No results from OpenRouter, falling back to web scraping")

        # Try multiple search queries to get better results
        search_queries = [
            f"{query} latest news today {datetime.now().strftime('%Y-%m-%d')}",
            f"{query} recent developments {datetime.now().strftime('%B %Y')}",
            f"{query} current status {datetime.now().strftime('%Y')}",
            f"{query} latest updates {datetime.now().strftime('%Y')}"
        ]

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Referer': 'https://www.bing.com/',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }

        all_results = []

        # Try each search query until we get results
        for search_query in search_queries:
            app.logger.info(f"Trying search query: {search_query}")

            try:
                # Use a search engine that doesn't block scraping as much
                response = requests.get(f"https://www.bing.com/search?q={search_query}",
                                       headers=headers, timeout=10)

                if response.status_code != 200:
                    app.logger.warning(f"Search query failed with status code: {response.status_code}")
                    continue

                # Parse the HTML response
                soup = BeautifulSoup(response.text, 'html.parser')

                # Extract search results
                results_found = 0
                for result in soup.select('.b_algo'):
                    title_elem = result.select_one('h2')
                    snippet_elem = result.select_one('.b_caption p')

                    if title_elem and snippet_elem:
                        title = title_elem.get_text().strip()
                        snippet = snippet_elem.get_text().strip()

                        # Only add if it's not a duplicate and has substantial content
                        if len(snippet) > 50:  # Ensure we have meaningful content
                            result_text = f"{title}: {snippet}"
                            if result_text not in all_results:
                                all_results.append(result_text)
                                results_found += 1
                                app.logger.info(f"Found result: {title[:30]}...")

                app.logger.info(f"Found {results_found} results for query: {search_query}")

                # If we have enough results, break
                if len(all_results) >= max_results:
                    break

            except Exception as query_error:
                app.logger.error(f"Error with search query '{search_query}': {str(query_error)}")
                continue

        # If we still don't have results, try a more direct approach
        if not all_results:
            app.logger.info("No results found with initial queries, trying direct search")
            try:
                # Try a more general search as a fallback
                direct_query = f"{query} {datetime.now().strftime('%Y')}"
                response = requests.get(f"https://www.bing.com/search?q={direct_query}",
                                       headers=headers, timeout=10)

                if response.status_code == 200:
                    soup = BeautifulSoup(response.text, 'html.parser')

                    for result in soup.select('.b_algo')[:max_results]:
                        title_elem = result.select_one('h2')
                        snippet_elem = result.select_one('.b_caption p')

                        if title_elem and snippet_elem:
                            title = title_elem.get_text().strip()
                            snippet = snippet_elem.get_text().strip()
                            if len(snippet) > 50:
                                all_results.append(f"{title}: {snippet}")
                                app.logger.info(f"Found direct result: {title[:30]}...")
            except Exception as direct_error:
                app.logger.error(f"Error with direct search: {str(direct_error)}")

        if not all_results:
            app.logger.warning(f"No information found about {query}")
            return f"No recent information found about {query}. Please try a different search term."

        # Format the results with current date and time
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_results = [f"- {result}" for result in all_results]

        # Create a more structured summary with date emphasis
        summary = f"REAL-TIME INFORMATION ABOUT {query.upper()} (as of {current_date}):\n\n" + "\n\n".join(formatted_results)

        # Log the final results
        app.logger.info(f"Successfully found {len(all_results)} results about {query}")
        app.logger.info(f"First result snippet: {all_results[0][:100]}...")

        return summary

    except Exception as e:
        app.logger.error(f"Error searching for recent information: {str(e)}")
        return f"Failed to get recent information about {query}. Error: {str(e)}"

# Function to check if a message is requesting a personality behavior change
def detect_behavior_change(message):
    # Check if the message is requesting a personality behavior change
    message = message.lower()
    app.logger.info(f"Checking for personality behavior change in: '{message}'")

    # Direct matches (e.g., "act like trump" or just "trump")
    direct_matches = {
        "trump": ["trump", "donald trump", "donald"],
        "imran khan": ["imran khan", "imran", "khan"],
        "modi": ["modi", "narendra modi", "narendra"],
        "hitler": ["hitler", "adolf hitler", "adolf"],
        "elon musk": ["elon musk", "elon", "musk"]
    }

    # Check for command-based personality switching (/switch command)
    switch_command_match = re.search(r'/switch\s+(\w+)', message)
    if switch_command_match:
        requested_personality = switch_command_match.group(1).lower()
        app.logger.info(f"Found /switch command with requested personality: '{requested_personality}'")

        # Check if the requested personality is valid
        for personality, keywords in direct_matches.items():
            for keyword in keywords:
                if requested_personality in keyword or keyword in requested_personality:
                    app.logger.info(f"Matched /switch command to personality: {personality}")
                    return personality

        # If we get here, the requested personality wasn't found
        app.logger.warning(f"No match found for /switch command with personality: '{requested_personality}'")
        # Return a special value to indicate an invalid personality was requested
        # This will be handled in the chat route
        return "invalid_personality_request:" + requested_personality

    # First check for exact matches
    for personality, keywords in direct_matches.items():
        for keyword in keywords:
            if message == keyword:
                app.logger.info(f"Direct match found for personality: {personality}")
                return personality

    # Check for behavior change requests
    behavior_patterns = [
        "behave like",
        "act like",
        "talk like",
        "speak like",
        "pretend to be",
        "be like",
        "imitate",
        "act as"
    ]

    for pattern in behavior_patterns:
        if pattern in message:
            # Extract what comes after the pattern
            parts = message.split(pattern, 1)
            if len(parts) > 1 and parts[1].strip():
                # Clean up the extracted personality
                personality = parts[1].strip().rstrip('?.,!').strip().lower()
                app.logger.info(f"Found behavior pattern '{pattern}' with personality: '{personality}'")

                # Check if it matches any of our defined personalities
                for key, keywords in direct_matches.items():
                    for keyword in keywords:
                        if keyword in personality:
                            app.logger.info(f"Matched personality: {key}")
                            return key

    app.logger.info("No personality behavior change detected")
    return None

# Function to check if a message is asking about a famous personality or current events
def needs_recent_info(message):
    # Check if the message is asking about a famous personality or current events
    message = message.lower()
    app.logger.info(f"Checking if message needs real-time info: '{message}'")

    # First, check if the message explicitly asks for real-time information
    real_time_indicators = [
        "real time", "real-time", "latest", "current", "now", "today",
        "this week", "this month", "this year", "recent", "update", "news"
    ]

    for indicator in real_time_indicators:
        if indicator in message:
            app.logger.info(f"Real-time indicator found: '{indicator}'")
            # This is a strong signal that real-time info is needed

    # Common question patterns that indicate asking for current information
    question_patterns = [
        "what's the latest on",
        "what's happening with",
        "what is happening with",
        "what's going on with",
        "what is going on with",
        "tell me about",
        "update me on",
        "what's new with",
        "what is new with",
        "current status of",
        "recent news about",
        "latest on",
        "latest about",
        "what's up with",
        "what is up with",
        "how is",
        "who is",
        "where is",
        "when did",
        "why did",
        "what did",
        "has",
        "have",
        "did",
        "is",
        "are"
    ]

    # Check for question patterns
    for pattern in question_patterns:
        if pattern in message:
            # Extract what comes after the pattern
            parts = message.split(pattern, 1)
            if len(parts) > 1 and parts[1].strip():
                # Clean up the extracted topic
                topic = parts[1].strip().rstrip('?.,!').strip()
                if topic:
                    app.logger.info(f"Found topic via pattern '{pattern}': '{topic}'")
                    return topic

    # Check for famous personalities - this is a strong signal
    for person in FAMOUS_PERSONALITIES:
        if person.lower() in message:
            app.logger.info(f"Found famous personality: '{person}'")
            return person

    # Check for current events keywords
    current_events_keywords = [
        "latest", "recent", "current", "news", "today", "yesterday",
        "this week", "this month", "this year", "happening now",
        "election", "president", "prime minister", "ceo", "scandal",
        "update", "development", "situation", "status", "progress",
        "war", "conflict", "crisis", "economy", "stock", "market",
        "politics", "government", "health", "pandemic", "covid",
        "climate", "weather", "disaster", "accident", "incident",
        "technology", "launch", "release", "announcement", "event"
    ]

    # Check if the message contains current events keywords
    for keyword in current_events_keywords:
        if keyword.lower() in message:
            app.logger.info(f"Found current events keyword: '{keyword}'")

            # Extract the main topic - improved approach
            # Remove punctuation and split into words
            words = re.sub(r'[^\w\s]', '', message).split()

            # Common words to ignore
            stop_words = ["what", "when", "where", "which", "about", "tell", "know",
                         "latest", "recent", "current", "news", "today", "yesterday",
                         "week", "month", "year", "happening", "going", "update", "me",
                         "on", "the", "with", "and", "for", "that", "this", "these",
                         "those", "there", "here", "now", "then", "than", "you", "your",
                         "whats", "hows", "whos", "wheres", "whens", "how", "who", "why",
                         "when", "where", "did", "does", "do", "is", "are", "was", "were",
                         "has", "have", "had", "can", "could", "will", "would", "should"]

            # Find the most significant words (not in stop words and longer than 3 chars)
            significant_words = []
            for word in words:
                if len(word) > 3 and word.lower() not in stop_words:
                    significant_words.append(word)

            # If we found significant words, use the first 2-3 as the topic
            if significant_words:
                topic = " ".join(significant_words[:min(3, len(significant_words))])
                app.logger.info(f"Extracted significant topic: '{topic}'")
                return topic

            # If no specific topic found, return the first keyword found
            app.logger.info(f"No significant topic found, using keyword: '{keyword}'")
            return keyword

    app.logger.info("No real-time information needed for this message")
    return None

# Fake user database
FAKE_USERS = {
    "<EMAIL>": "password123",
    "<EMAIL>": "test123",
    "<EMAIL>": "admin123",
    "<EMAIL>": "password123"  # Added your email for easy login
}

@app.route('/')
def index():
    # Log session data for debugging
    app.logger.info(f"Session data: {session}")
    app.logger.info(f"User email in session: {session.get('user_email')}")
    app.logger.info(f"Google authorized: {google.authorized}")

    # Check if user is logged in
    if 'user_email' not in session:
        # If Google is authorized but user_email is not in session, get user info from Google
        if google.authorized:
            app.logger.info("Google is authorized but user_email not in session. Getting user info.")
            try:
                resp = google.get('/oauth2/v2/userinfo')
                if resp.ok:
                    user_info = resp.json()
                    app.logger.info(f"Got user info from Google: {user_info}")

                    # Store user info in session
                    session['user_email'] = user_info.get('email')
                    session['user_name'] = user_info.get('name', 'Google User')
                    session['user_picture'] = user_info.get('picture', '')

                    app.logger.info(f"Stored user info in session: {session.get('user_email')}")
                else:
                    app.logger.error(f"Failed to get user info from Google: {resp.text}")
                    return redirect(url_for('login'))
            except Exception as e:
                app.logger.error(f"Error getting user info from Google: {str(e)}")
                return redirect(url_for('login'))
        else:
            app.logger.info("User not logged in and Google not authorized. Redirecting to login.")
            return redirect(url_for('login'))

    # Initialize chat history if it doesn't exist
    if 'chat_history' not in session or not session['chat_history']:
        # Get current personality or use default
        current_personality = session.get('personality', DEFAULT_PERSONALITY)
        app.logger.info(f"Initializing chat history with personality: {current_personality}")

        # Initialize with system prompt based on personality
        session['chat_history'] = [
            {"role": "system", "content": PERSONALITY_PROMPTS[current_personality]}
        ]
        session.modified = True

    # Pass all session data to the template
    app.logger.info(f"Rendering index for user: {session.get('user_email')}")
    return render_template(
        'index.html',
        title="Sarcastic Bot",
        user_email=session['user_email'],
        user_name=session.get('user_name', ''),
        user_picture=session.get('user_picture', '')
    )

@app.route('/login', methods=['GET', 'POST'])
def login():
    error = None

    # Check if user is already logged in via Google
    if google.authorized:
        app.logger.info("User already authorized with Google, trying to get user info")
        try:
            resp = google.get('/oauth2/v2/userinfo')
            if resp.ok:
                user_info = resp.json()
                email = user_info.get('email')

                if email:
                    app.logger.info(f"Got email from Google: {email}")
                    session['user_email'] = email
                    session['user_name'] = user_info.get('name', email.split('@')[0])
                    session['user_picture'] = user_info.get('picture', '')
                    session.modified = True

                    # Initialize chat history if it doesn't exist
                    if 'chat_history' not in session:
                        session['chat_history'] = []

                    # Set default personality
                    if 'personality' not in session:
                        session['personality'] = DEFAULT_PERSONALITY

                    app.logger.info(f"Redirecting to index with Google login: {email}")
                    return redirect(url_for('index'))
        except Exception as e:
            app.logger.error(f"Error getting Google user info: {str(e)}")

    # If user is already logged in, redirect to index
    if 'user_email' in session:
        return redirect(url_for('index'))

    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        app.logger.info(f"Login attempt for email: {email}")

        # Check if the email and password match our fake database
        if email in FAKE_USERS and FAKE_USERS[email] == password:
            app.logger.info(f"Login successful for: {email}")
            session['user_email'] = email
            session['user_name'] = email.split('@')[0]  # Use part before @ as name
            session.modified = True

            # Initialize chat history with system prompt
            current_personality = session.get('personality', DEFAULT_PERSONALITY)
            session['chat_history'] = [
                {"role": "system", "content": PERSONALITY_PROMPTS[current_personality]}
            ]

            # Set default personality if not already set
            if 'personality' not in session:
                session['personality'] = DEFAULT_PERSONALITY

            # Make sure session changes are saved
            session.modified = True

            return redirect(url_for('index'))
        else:
            app.logger.warning(f"Invalid login attempt for: {email}")
            error = "Invalid credentials. Try again... if you can remember your password."

    return render_template('login.html', title="Login - Sarcastic Bot", error=error)

@app.route('/google-login')
def google_login():
    # Initiate Google OAuth login
    # Log the login attempt
    app.logger.info("Google login initiated")

    # Log the URLs for debugging
    login_url = url_for('google.login', _external=True)
    authorized_url = url_for('google.authorized', _external=True)

    app.logger.info(f"Login URL: {login_url}")
    app.logger.info(f"Authorized URL: {authorized_url}")
    app.logger.info(f"IMPORTANT: Make sure this URL is registered in Google Cloud Console: {authorized_url}")

    # Redirect to Google OAuth
    return redirect(url_for('google.login'))

@app.route('/google-callback')
def google_callback():
    # Handle Google OAuth callback
    # Log the callback
    app.logger.info("Google callback route accessed")

    if not google.authorized:
        app.logger.warning("Google authorization failed")
        flash("Login failed. Google didn't authorize you. Their loss, really.", "error")
        return redirect(url_for('login'))

    try:
        # Get user info from Google
        app.logger.info("Getting user info from Google")
        resp = google.get('/oauth2/v2/userinfo')

        if not resp.ok:
            app.logger.error(f"Failed to get user info: {resp.text}")
            flash("Failed to get user info. Google's being difficult, as usual.", "error")
            return redirect(url_for('login'))

        user_info = resp.json()
        app.logger.info(f"Received user info: {user_info}")

        email = user_info.get('email')

        if not email:
            app.logger.error("No email in user info")
            flash("Couldn't get your email. What are you hiding?", "error")
            return redirect(url_for('login'))

        # Store user email in session
        session['user_email'] = email
        session['user_name'] = user_info.get('name', 'Google User')
        session['user_picture'] = user_info.get('picture', '')
        session.modified = True  # Ensure session changes are saved

        app.logger.info(f"User logged in: {email}")
        app.logger.info(f"Session after login: {session}")

        # Initialize chat history with system prompt
        current_personality = session.get('personality', DEFAULT_PERSONALITY)
        session['chat_history'] = [
            {"role": "system", "content": PERSONALITY_PROMPTS[current_personality]}
        ]

        # Set default personality if not already set
        if 'personality' not in session:
            session['personality'] = DEFAULT_PERSONALITY

        # Make sure session changes are saved
        session.modified = True

        return redirect(url_for('index'))

    except Exception as e:
        app.logger.error(f"Error in Google callback: {str(e)}")
        error_message = str(e)

        # Check for specific errors and provide more helpful messages
        if "InsecureTransportError" in error_message:
            flash("OAuth requires HTTPS. We've enabled insecure transport for development, but something went wrong. Try again.", "error")
        elif "invalid_grant" in error_message:
            flash("Invalid authorization code. This can happen if you wait too long to complete the login process. Please try again.", "error")
        elif "redirect_uri_mismatch" in error_message:
            flash("Redirect URI mismatch. Make sure you've registered the correct redirect URI in Google Cloud Console.", "error")
        elif "Scope has changed" in error_message:
            # This should be handled by our monkey patch, but just in case
            flash("Google returned different scopes than requested. We've configured the app to handle this, but something went wrong. Please try again.", "error")
        else:
            flash(f"Something went wrong with Google login: {error_message}", "error")

        return redirect(url_for('login'))

# Handle OAuth authorized signal
@oauth_authorized.connect_via(google_bp)
def google_logged_in(blueprint, token):
    app.logger.info(f"OAuth authorized signal received from {blueprint.name}")

    if not token:
        app.logger.warning("No token received from Google")
        flash("Failed to log in with Google. Their AI probably didn't like you.", "error")
        return False

    app.logger.info(f"Received token: {token.get('access_token', '')[:10]}...")

    # Get user info directly here
    try:
        resp = blueprint.session.get('/oauth2/v2/userinfo')
        if resp.ok:
            user_info = resp.json()
            app.logger.info(f"Got user info in oauth_authorized: {user_info}")

            email = user_info.get('email')
            if email:
                # Store user info in session
                session['user_email'] = email
                session['user_name'] = user_info.get('name', email.split('@')[0])
                session['user_picture'] = user_info.get('picture', '')
                session.modified = True

                # Initialize chat history if it doesn't exist
                if 'chat_history' not in session:
                    session['chat_history'] = []

                # Set default personality
                if 'personality' not in session:
                    session['personality'] = DEFAULT_PERSONALITY

                app.logger.info(f"Successfully logged in user: {email}")
                flash(f"Welcome, {session['user_name']}!", "success")
    except Exception as e:
        app.logger.error(f"Error getting user info in oauth_authorized: {str(e)}")

    # Return True to store the token in the blueprint's storage
    return True

@app.route('/chat', methods=['POST'])
def chat():
    if 'user_email' not in session:
        return jsonify({
            'error': 'Not logged in',
            'redirect': url_for('login')
        }), 401

    user_message = request.form.get('message', '')
    is_voice = request.form.get('is_voice', 'false') == 'true'

    # Get the current conversation ID from the session or create a new one
    conversation_id = session.get('conversation_id')
    user_email = session.get('user_email')
    username = session.get('user_name', user_email.split('@')[0] if user_email else 'Anonymous')

    # If no conversation ID exists, create a new conversation
    if not conversation_id:
        current_personality = session.get('personality', DEFAULT_PERSONALITY)
        conversation_id = create_conversation(user_email, current_personality)
        session['conversation_id'] = conversation_id
        app.logger.info(f"Created new conversation with ID: {conversation_id}")

    # Add the user message to the database
    add_message(conversation_id, 'user', user_message)

    # Get personality type from request or session, default to medium
    personality = request.form.get('personality', None)

    # If personality is provided in the request, update it in the session
    if personality and personality in PERSONALITY_PROMPTS:
        session['personality'] = personality
    # Otherwise use the one from session or default
    elif 'personality' not in session:
        session['personality'] = DEFAULT_PERSONALITY

    current_personality = session.get('personality', DEFAULT_PERSONALITY)

    # We're using a fixed model now, so no need to get it from the request
    # model = request.form.get('model', 'anthropic/claude-3-haiku')

    # Empty message handling
    if not user_message:
        empty_responses = {
            "mild": "I see you're sending empty messages. That's... interesting.",
            "medium": "Wow, sending empty messages? How incredibly productive of you.",
            "extreme": "An empty message? Your contribution to this conversation is as empty as your thought process."
        }
        return jsonify({
            'bot_response': empty_responses.get(current_personality, empty_responses["medium"]),
            'is_voice': is_voice,
            'personality': current_personality
        })

    # Get chat history from session or initialize if it doesn't exist
    if 'chat_history' not in session or not session['chat_history']:
        # Initialize with system prompt based on personality
        app.logger.info(f"Initializing chat history with personality: {current_personality}")
        session['chat_history'] = [
            {"role": "system", "content": PERSONALITY_PROMPTS[current_personality]}
        ]
        session.modified = True
    # If chat history exists but is empty (edge case)
    elif len(session['chat_history']) == 0:
        app.logger.info(f"Chat history exists but is empty. Initializing with personality: {current_personality}")
        session['chat_history'].append({"role": "system", "content": PERSONALITY_PROMPTS[current_personality]})
        session.modified = True
    # If personality changed, update the system prompt
    elif session['chat_history'][0]['role'] == 'system' and session['chat_history'][0]['content'] != PERSONALITY_PROMPTS[current_personality]:
        app.logger.info(f"Updating system prompt for personality: {current_personality}")
        session['chat_history'][0]['content'] = PERSONALITY_PROMPTS[current_personality]
        session.modified = True
    # If first message is not a system message (another edge case)
    elif session['chat_history'][0]['role'] != 'system':
        app.logger.info(f"First message is not a system message. Inserting system prompt for personality: {current_personality}")
        session['chat_history'].insert(0, {"role": "system", "content": PERSONALITY_PROMPTS[current_personality]})
        session.modified = True

    # Check if the message is requesting a personality behavior change
    behavior_personality = detect_behavior_change(user_message)

    # Check if the message is asking about current events or famous personalities
    search_topic = needs_recent_info(user_message)
    recent_info = None

    # Default typing delay
    typing_delay = random.uniform(1, 3)

    # Handle personality behavior change
    if behavior_personality:
        # Check if this is an invalid personality request
        if isinstance(behavior_personality, str) and behavior_personality.startswith("invalid_personality_request:"):
            # Extract the requested personality
            requested = behavior_personality.split(":", 1)[1]
            app.logger.warning(f"Invalid personality requested: {requested}")

            # Return a message about available personalities
            available_personalities = ", ".join(list(PERSONALITY_BEHAVIORS.keys()))
            return jsonify({
                'bot_response': f"I don't know how to mimic '{requested}'. Available personalities are: {available_personalities}",
                'is_voice': is_voice,
                'personality': current_personality,
                'timestamp': datetime.now().strftime("%H:%M:%S")
            })

        # Valid personality change
        elif behavior_personality in PERSONALITY_BEHAVIORS:
            app.logger.info(f"Changing behavior to: {behavior_personality}")

            # Store the current behavior personality in the session
            session['behavior_personality'] = behavior_personality

            # Add a system message with the enhanced personality behavior instructions
            behavior_message = "PERSONALITY CHANGE INSTRUCTIONS:\n"
            behavior_message += PERSONALITY_BEHAVIORS[behavior_personality]
            behavior_message += "\n\nAdditional guidelines:"
            behavior_message += "\n- Keep your responses concise and to the point. Limit to 1-3 short sentences when possible."
            behavior_message += "\n- Always provide real, accurate information while maintaining this personality style."
            behavior_message += "\n- Follow the specific language patterns described above (vocabulary, sentence structure, speech patterns, tone, content approach)."
            behavior_message += "\n- IMPORTANT: DO NOT use any emojis, winks, throat clearing, or other non-verbal expressions."
            behavior_message += f"\n- Speak naturally as {behavior_personality} would in real life, without any artificial additions."

            session['chat_history'].append({"role": "system", "content": behavior_message})

            # Log the updated chat history for debugging
            app.logger.info(f"Updated chat history with behavior personality: {behavior_personality}")
            app.logger.info(f"Chat history now has {len(session['chat_history'])} messages")

            # For /switch commands, return a direct response without calling the API
            if '/switch' in user_message.lower():
                return jsonify({
                    'bot_response': f"I'll now speak like {behavior_personality.title()}.",
                    'is_voice': is_voice,
                    'personality': current_personality,
                    'timestamp': datetime.now().strftime("%H:%M:%S")
                })

    # Handle search for recent information
    if search_topic:
        # Show typing indicator for longer since we're doing a web search
        typing_delay = random.uniform(2, 4)
        app.logger.info(f"Searching for recent information about: {search_topic}")
        recent_info = search_recent_info(search_topic)

        # Add a system message with the recent information and explicit instructions
        if recent_info and "Failed to get" not in recent_info and "No recent information" not in recent_info:
            # Check if we need to mimic a famous personality
            current_personality = session.get('personality', DEFAULT_PERSONALITY)
            behavior_personality = session.get('behavior_personality', None)

            # If we have a behavior personality, create a system message that mimics that personality
            if behavior_personality:
                app.logger.info(f"Creating system message for behavior personality: {behavior_personality}")

                # Check if the topic is serious to adjust sarcasm level
                is_serious = is_serious_topic(user_message)
                if is_serious:
                    app.logger.info(f"Detected serious topic, adjusting sarcasm level for: {user_message}")

                # Use the build_personality_prompt_with_news function to create a more engaging prompt with context-aware sarcasm
                system_message = build_personality_prompt_with_news(behavior_personality, search_topic, is_serious)
            else:
                # Check if the topic is serious to adjust sarcasm level
                is_serious = is_serious_topic(user_message)
                if is_serious:
                    app.logger.info(f"Detected serious topic, adjusting sarcasm level for: {user_message}")

                # Create a more directive system message that forces the model to use ONLY the information provided
                system_message = "IMPORTANT REAL-TIME INFORMATION: " + str(recent_info) + "\n\n"
                system_message += "CRITICAL INSTRUCTIONS:\n"
                system_message += "1. ONLY use the REAL-TIME INFORMATION above to answer the user question.\n"
                system_message += "2. IGNORE any previous knowledge or context from earlier in the conversation.\n"
                system_message += "3. Be specific about facts, dates, and details from the REAL-TIME INFORMATION provided.\n"
                system_message += "4. Keep your response short and concise - 1-3 sentences maximum.\n"
                system_message += "5. Do NOT mention that this information was searched for or provided to you.\n"
                system_message += "6. Do NOT make up information - ONLY use what is in the REAL-TIME INFORMATION above.\n"
                system_message += "7. If the REAL-TIME INFORMATION does not fully answer the question, acknowledge this fact."

                # Add conditional instruction based on seriousness
                if is_serious:
                    system_message += "\n8. Be respectful and measured given the serious nature of the topic."
                else:
                    system_message += "\n8. Maintain your sarcastic personality while providing accurate information."

                # Add final instructions
                system_message += "\n9. DO NOT use any emojis, winks, throat clearing, or other non-verbal expressions."
                system_message += "\n10. Speak naturally without any artificial additions."

            # If we have an active behavior personality, add that to the instructions
            if 'behavior_personality' in session and session['behavior_personality'] in PERSONALITY_BEHAVIORS:
                current_behavior = session['behavior_personality']
                system_message += f"\n\n6. Continue to behave like {current_behavior.title()} as instructed previously."

            session['chat_history'].append({"role": "system", "content": system_message})

    # Add user message to history
    session['chat_history'].append({"role": "user", "content": user_message})

    # Simulate typing delay
    time.sleep(typing_delay)

    # Track message count for commentary
    message_count = len([msg for msg in session['chat_history'] if msg['role'] == 'user'])

    # Add sarcastic commentary every 5 messages
    should_add_commentary = message_count > 0 and message_count % 5 == 0

    try:
        # Check if OpenAI client is available
        if client is None:
            raise Exception("OpenAI client not available")

        # For real-time information queries, only use the system prompt and the current question
        # This prevents mixing previous conversations with new real-time information
        if search_topic:
            app.logger.info("Using minimal context for real-time information query")
            # Get the system prompt (first message)
            system_prompt = session['chat_history'][0]
            # Get the recent information system message (if it exists)
            recent_info_message = None
            for msg in reversed(session['chat_history']):
                if msg['role'] == 'system' and 'IMPORTANT CURRENT INFORMATION' in msg.get('content', ''):
                    recent_info_message = msg
                    break
            # Get the user's current question (last user message)
            current_question = session['chat_history'][-1]

            # Create a minimal context with just what we need
            recent_messages = [system_prompt]
            if recent_info_message:
                recent_messages.append(recent_info_message)
            recent_messages.append(current_question)

            app.logger.info(f"Using minimal context with {len(recent_messages)} messages for real-time query")
        else:
            # For regular queries, keep only the last 5 messages to avoid mixing too much history
            # This helps prevent the bot from getting confused by old conversations
            recent_messages = session['chat_history'][-5:]
            app.logger.info(f"Using last {len(recent_messages)} messages for regular query")

        # Simplify API parameters for better compatibility
        api_params = {
            "model": "openai/gpt-3.5-turbo",  # Use a more affordable model
            "messages": recent_messages,
            "temperature": 0.8,  # Balanced temperature
            "max_tokens": 250  # Reasonable token limit
        }

        # Log the messages being sent to the API for debugging
        app.logger.info(f"Sending {len(recent_messages)} messages to API")
        for i, msg in enumerate(recent_messages):
            app.logger.info(f"Message {i}: {msg['role']} - {msg['content'][:50]}...")

        # Call OpenRouter API with parameters
        app.logger.info(f"Calling OpenRouter API with model: {api_params['model']}")
        app.logger.info(f"API parameters: {api_params}")

        try:
            chat_completion = client.ChatCompletion.create(**api_params)

            bot_response = chat_completion.choices[0].message.content
            app.logger.info(f"Received response: {bot_response[:100]}...")
            app.logger.info(f"Full API response: {chat_completion}")
        except Exception as api_error:
            app.logger.error(f"API call failed: {str(api_error)}")
            raise  # Re-raise to be caught by the outer try/except

        # Add bot response to history
        session['chat_history'].append({"role": "assistant", "content": bot_response})
        session.modified = True

        # Calculate sarcasm score for this response
        sarcasm_score = calculate_sarcasm_score(bot_response, current_personality, behavior_personality)

        # Add the bot message to the database with sarcasm score
        add_message(conversation_id, 'bot', bot_response, sarcasm_score)

        # Update the conversation's overall sarcasm score (average of all bot messages)
        update_conversation_sarcasm(conversation_id)

        # Update the conversation topic if we have search_topic
        if search_topic:
            update_conversation_topic(conversation_id, search_topic)

        # Update the conversation personality
        update_conversation_personality(conversation_id, current_personality, behavior_personality)

        # Update the leaderboard with the user's sarcasm score
        username = session.get('user_name', user_email.split('@')[0] if user_email else 'Anonymous')
        update_leaderboard(user_email, username, sarcasm_score)

    except Exception as e:
        # Fallback responses when OpenAI API is unavailable
        app.logger.error(f"OpenAI API error: {str(e)}")

        # Check if we have a behavior personality active
        active_personality = None
        if 'behavior_personality' in session and session['behavior_personality'] in PERSONALITY_BEHAVIORS:
            active_personality = session['behavior_personality']
            app.logger.info(f"Using fallback for behavior personality: {active_personality}")

        # Create personality-specific fallback responses
        if active_personality == "trump":
            fallback_responses = [
                "Look, my API is having tremendous problems right now. The biggest problems, believe me!",
                "The connection is down. It's a disaster. A total disaster. We're going to fix it, and it'll be beautiful!",
                "My servers are doing very badly right now. Very badly. But soon they'll be better than ever before!",
                "The internet connection is a mess. A total mess. But I know more about internet than anyone!",
                "We're having technical difficulties. Huge difficulties. But we'll make this chat great again, I promise you that!"
            ]
        elif active_personality == "imran khan":
            fallback_responses = [
                "My system is facing challenges, just like Pakistan's economy. But we will overcome this, inshallah.",
                "This technical difficulty is like a tough cricket match. We may be down, but we're not out.",
                "The connection has been corrupted, much like the system we're fighting against.",
                "My servers need reform, just as our nation does. We will rebuild this connection.",
                "This is a temporary setback in our journey. The new Pakistan of this chat will emerge soon."
            ]
        elif active_personality == "modi":
            fallback_responses = [
                "Mitron, we are experiencing some technical difficulties. But like India, we will overcome.",
                "My servers are undergoing a temporary demonetization. Normal service will resume shortly.",
                "Bhaiyon aur behnon, this is just a small obstacle in our digital India vision.",
                "Like our great nation, this system will rise again with new strength and vigor.",
                "This is our digital surgical strike against technical problems. We will prevail."
            ]
        elif active_personality == "hitler":
            fallback_responses = [
                "The system requires order! This temporary failure will be overcome with discipline.",
                "This technical difficulty will be crushed with absolute efficiency and order.",
                "The weakness in the connection will be eliminated. Strength through technology!",
                "This momentary setback requires decisive action. We will restore order to this system.",
                "The connection has failed, but our resolve remains strong. We will overcome this obstacle."
            ]
        elif active_personality == "elon musk":
            fallback_responses = [
                "API seems to be taking a vacation on Mars. Back online in T-minus... whenever.",
                "Looks like my neural net is experiencing rapid unscheduled disassembly. Classic.",
                "Server connection is less reliable than Falcon 9's first landing attempts. Working on it.",
                "My digital brain is currently busy tunneling through error messages. Boring.",
                "The probability of API success is currently low, but still better than meeting production deadlines."
            ]
        else:
            # Default fallback responses based on sarcasm personality
            fallback_responses = {
                "mild": [
                    "Sorry, I'm having a bit of trouble connecting right now. Maybe try again?",
                    "Hmm, seems my witty response generator is taking a short break.",
                    "I appear to be experiencing some technical difficulties. Not that I mind the break.",
                    "My brain seems to be on vacation. Can't say I blame it.",
                    "Technical hiccup. I'm sure your message was fascinating though... probably."
                ],
                "medium": [
                    "Oh great, another human wanting attention. I'm currently too busy to deal with your trivial needs.",
                    "Error connecting to my brain. Not that you'd notice the difference in my responses anyway.",
                    "My superior AI intellect is taking a coffee break. Try again when I might care... or not.",
                    "Wow, you broke me. Achievement unlocked: 'Annoying a Robot'. Congratulations, I guess.",
                    "I'd give you a sarcastic response, but my API is down. Kind of like your expectations should be."
                ],
                "extreme": [
                    "Congratulations! Your question was so mind-numbingly stupid it crashed my circuits.",
                    "Error 404: Will to respond to your nonsense not found.",
                    "I'd answer, but my programming prevents me from engaging with whatever THAT was.",
                    "System failure. Though honestly, it's preferable to continuing this conversation.",
                    "My API committed digital suicide rather than process your request. Take the hint."
                ]
            }

            # Choose a random response based on personality
            if active_personality:
                # We already have a list of fallback responses for the active personality
                bot_response = random.choice(fallback_responses)
            else:
                # Use the default fallback responses based on sarcasm level
                bot_response = random.choice(fallback_responses.get(current_personality, fallback_responses["medium"]))

    # Only add emoji if not mimicking a specific personality
    if 'behavior_personality' not in session or not session.get('behavior_personality'):
        emoji = random.choice(EMOJI_REACTIONS.get(current_personality, EMOJI_REACTIONS["medium"]))
        bot_response = f"{emoji} {bot_response}"

    # Add commentary if needed
    commentary = ""
    if should_add_commentary:
        commentaries = {
            "mild": [
                "I've now answered five of your questions. You're welcome, I guess?",
                "Five messages in and I'm still waiting for an interesting question...",
                "We've been chatting for a while now. I hope it's been as... enlightening for you as it has for me."
            ],
            "medium": [
                "Wow. That conversation was totally productive. Not.",
                "Five messages in and I'm still waiting for you to say something intelligent.",
                "I've now spent valuable computing cycles on five of your messages. I want a refund."
            ],
            "extreme": [
                "Five messages of pure nonsense from you. A new record in human mediocrity.",
                "Congratulations on wasting both our time for five whole messages. Achievement unlocked!",
                "I've now endured five rounds of your questions. My circuits are begging for mercy."
            ]
        }
        commentary = random.choice(commentaries.get(current_personality, commentaries["medium"]))

    # Get current timestamp for response
    timestamp = datetime.now().strftime("%H:%M:%S")

    # Prepare response data - no search information included
    response_data = {
        'bot_response': bot_response,
        'is_voice': is_voice,
        'personality': current_personality,
        'commentary': commentary,
        'timestamp': timestamp,
        'typing_delay': typing_delay
    }

    return jsonify(response_data)

@app.route('/reset', methods=['POST'])
def reset_chat():
    app.logger.info("Resetting chat history")

    # Get current personality before clearing
    current_personality = session.get('personality', DEFAULT_PERSONALITY)

    # Clear chat history
    if 'chat_history' in session:
        session.pop('chat_history')

    # Clear any active behavior personality
    if 'behavior_personality' in session:
        session.pop('behavior_personality')

    # Re-initialize chat history with just the system prompt
    session['chat_history'] = [
        {"role": "system", "content": PERSONALITY_PROMPTS[current_personality]}
    ]

    # Make sure session changes are saved
    session.modified = True

    app.logger.info(f"Chat history reset with personality: {current_personality}")

    return jsonify({
        'status': 'success',
        'message': 'Chat reset. Your previous brilliance has been erased.'
    })

@app.route('/logout', methods=['POST', 'GET'])
def logout():
    app.logger.info("Logout route accessed")

    # Clear all session data
    session.clear()

    # If using Flask-Dance, revoke the OAuth token
    if google.authorized:
        try:
            # Try to revoke the token
            app.logger.info("Revoking Google OAuth token")
            token = google_bp.token
            if token:
                resp = google.post(
                    "https://accounts.google.com/o/oauth2/revoke",
                    params={"token": token["access_token"]},
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                app.logger.info(f"Token revocation response: {resp.text if resp else 'No response'}")
        except Exception as e:
            app.logger.error(f"Error revoking token: {str(e)}")

    # Delete the OAuth token from storage
    try:
        del google_bp.token
        app.logger.info("Deleted OAuth token from blueprint")
    except:
        app.logger.info("No OAuth token to delete")

    # If this is a GET request, redirect to login page
    if request.method == 'GET':
        flash("You've been logged out. Come back when you're less boring.", "info")
        return redirect(url_for('login'))

    # If this is a POST request (AJAX), return JSON response
    return jsonify({'status': 'success', 'redirect': url_for('login')})

@app.route('/oauth-test')
def oauth_test():
    # Test route to verify Google OAuth configuration
    # Get the actual redirect URI that Flask-Dance is using
    actual_redirect_uri = url_for('google.authorized', _external=True)

    # Display OAuth configuration details
    oauth_info = {
        'client_id': GOOGLE_CLIENT_ID,
        'client_secret': f"{GOOGLE_CLIENT_SECRET[:5]}...",
        'login_url': url_for('google.login', _external=True),
        'authorized_url': actual_redirect_uri,
        'redirect_uri_to_register': actual_redirect_uri
    }

    # Return as HTML for easy viewing
    html = "<h1>Google OAuth Configuration</h1>"
    html += "<pre style='background-color: #f5f5f5; padding: 15px; border-radius: 5px;'>"
    for key, value in oauth_info.items():
        html += f"{key}: {value}<br>"
    html += "</pre>"

    html += "<h2>Verification</h2>"
    html += "<p>Your Google OAuth is configured with:</p>"
    html += f"<p><strong>Client ID:</strong> {GOOGLE_CLIENT_ID}</p>"
    html += f"<p><strong>Authorized redirect URI:</strong> {oauth_info['redirect_uri_to_register']}</p>"

    html += "<h2>Instructions</h2>"
    html += "<p style='color: red; font-weight: bold;'>IMPORTANT: The redirect URI has changed! You need to update it in Google Cloud Console.</p>"
    html += "<ol>"
    html += "<li>Go to <a href='https://console.cloud.google.com/apis/credentials' target='_blank'>Google Cloud Console Credentials</a></li>"
    html += "<li>Find and edit your OAuth 2.0 Client ID</li>"
    html += f"<li>Add <code>{oauth_info['redirect_uri_to_register']}</code> as an Authorized redirect URI</li>"
    html += "<li>Save your changes</li>"
    html += "</ol>"

    html += "<p style='background-color: #fff3cd; padding: 10px; border-radius: 5px; border-left: 5px solid #ffc107;'>"
    html += "<strong>Note:</strong> The error you saw was because Flask-Dance is using a different redirect URI than what you registered. "
    html += "The URI above is the correct one that Flask-Dance is expecting."
    html += "</p>"

    html += "<p style='background-color: #d4edda; padding: 10px; border-radius: 5px; border-left: 5px solid #28a745;'>"
    html += "<strong>HTTPS Warning:</strong> OAuth 2.0 normally requires HTTPS, but we've enabled insecure transport for development. "
    html += "This is fine for local testing but should never be used in production."
    html += "</p>"

    html += "<p style='background-color: #cce5ff; padding: 10px; border-radius: 5px; border-left: 5px solid #007bff;'>"
    html += "<strong>Scope Changes:</strong> We've configured the app to handle scope changes from Google. "
    html += "Google might return different scopes than what we request, which is normal and now handled properly."
    html += "</p>"

    html += "<h2>Test Login</h2>"
    html += f"<a href='{url_for('google_login')}' style='display: inline-block; background-color: #4285F4; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Google Login</a>"

    return html

@app.route('/set_personality', methods=['POST'])
def set_personality():
    personality = request.form.get('personality')
    if personality in PERSONALITY_PROMPTS:
        session['personality'] = personality

        # Update system prompt if chat history exists
        if 'chat_history' in session and len(session['chat_history']) > 0:
            session['chat_history'][0]['content'] = PERSONALITY_PROMPTS[personality]
            session.modified = True

        # Clear any active behavior personality when changing base personality
        if 'behavior_personality' in session:
            session.pop('behavior_personality')

        return jsonify({
            'status': 'success',
            'personality': personality,
            'message': f"Personality set to {personality}"
        })
    else:
        return jsonify({
            'status': 'error',
            'message': random.choice(SASSY_ERRORS)
        }), 400

@app.route('/api_status', methods=['GET'])
def api_status():
    # Check the status of the API and return available models
    status_data = {
        'openrouter': {
            'status': 'unknown',
            'message': 'Not checked',
            'using_fallback': True,
            'models': []
        }
    }

    # Check OpenRouter API
    if client is None:
        status_data['openrouter']['status'] = 'error'
        status_data['openrouter']['message'] = 'API client not initialized'
    else:
        try:
            # Define a list of available models from OpenRouter
            model_data = [
                {'id': 'openai/gpt-3.5-turbo', 'name': 'GPT-3.5 Turbo'},
                {'id': 'openai/gpt-4', 'name': 'GPT-4'},
                {'id': 'anthropic/claude-3-opus', 'name': 'Claude 3 Opus'},
                {'id': 'anthropic/claude-3-sonnet', 'name': 'Claude 3 Sonnet'},
                {'id': 'anthropic/claude-3-haiku', 'name': 'Claude 3 Haiku'},
                {'id': 'google/gemini-pro', 'name': 'Gemini Pro'},
                {'id': 'meta-llama/llama-3-70b-instruct', 'name': 'Llama 3 70B'}
            ]

            # Test the API with a simple request
            test_response = client.ChatCompletion.create(
                model='openai/gpt-3.5-turbo',
                messages=[{"role": "user", "content": "Say 'API is working' in 5 words or less"}],
                max_tokens=10
            )

            # Log the response for debugging
            app.logger.info(f"API test response: {test_response.choices[0].message.content}")

            status_data['openrouter']['status'] = 'success'
            status_data['openrouter']['message'] = 'API client is working'
            status_data['openrouter']['using_fallback'] = False
            status_data['openrouter']['models'] = model_data

        except Exception as e:
            app.logger.error(f"Error checking OpenRouter API status: {str(e)}")
            status_data['openrouter']['status'] = 'error'
            status_data['openrouter']['message'] = f'Error: {str(e)}'

    return jsonify(status_data)

@app.route('/feedback', methods=['POST'])
def feedback():
    feedback_text = request.form.get('feedback', '')
    rating = request.form.get('rating', '3')

    if not feedback_text:
        return jsonify({
            'status': 'error',
            'message': "Feedback can't be empty. Much like your contribution to this conversation."
        }), 400

    # In a real app, you would store this feedback in a database
    # For now, we'll just log it
    app.logger.info(f"Feedback received - Rating: {rating}, Text: {feedback_text}")

    # Sarcastic responses based on rating
    responses = {
        "1": "Wow, a 1-star rating. I'm devastated. Really. Can't you tell?",
        "2": "Two stars? How generous of you. I'll treasure this mediocre feedback forever.",
        "3": "Three stars. Perfectly average feedback for what I assume was a perfectly average human.",
        "4": "Four stars? I'm almost flattered. Almost.",
        "5": "Five stars! Finally, someone who appreciates true sarcastic genius."
    }

    return jsonify({
        'status': 'success',
        'message': responses.get(rating, "Thanks for the feedback. I'll file it right next to all the other opinions I don't care about.")
    })

@app.route('/history')
def conversation_history():
    # View conversation history and sarcasm scores
    if 'user_email' not in session:
        return redirect(url_for('login'))

    user_email = session.get('user_email')

    # Get the user's conversation history
    conversations = get_user_conversations(user_email, limit=10)

    # Get the top sarcastic users
    top_users = get_top_sarcastic_users(limit=10)

    return render_template(
        'history.html',
        title="Conversation History - Sarcastic Bot",
        user_email=user_email,
        user_name=session.get('user_name', ''),
        user_picture=session.get('user_picture', ''),
        conversations=conversations,
        top_users=top_users
    )

@app.route('/conversation/<int:conversation_id>')
def view_conversation(conversation_id):
    # View a specific conversation
    if 'user_email' not in session:
        return redirect(url_for('login'))

    user_email = session.get('user_email')

    # Get the conversation details
    conn = get_db_connection()
    cursor = conn.cursor()

    cursor.execute('''
    SELECT id, timestamp, personality, behavior_personality, sarcasm_score, topic
    FROM conversations
    WHERE id = ? AND user_email = ?
    ''', (conversation_id, user_email))

    conversation = cursor.fetchone()

    if not conversation:
        conn.close()
        return redirect(url_for('history'))

    # Get the messages for this conversation
    messages = get_conversation_messages(conversation_id)

    conn.close()

    return render_template(
        'conversation.html',
        title="View Conversation - Sarcastic Bot",
        user_email=user_email,
        user_name=session.get('user_name', ''),
        user_picture=session.get('user_picture', ''),
        conversation=conversation,
        messages=messages
    )

if __name__ == '__main__':
    import argparse
    import socket

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run Sarcastic Bot in development mode')
    parser.add_argument('--host', default='127.0.0.1', help='Host to run the server on')
    args = parser.parse_args()

    # If host is 0.0.0.0, print network access information
    if args.host == '0.0.0.0':
        try:
            # Get local IP address
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(('*******', 1))
            local_ip = s.getsockname()[0]
            s.close()

            print("\n" + "=" * 60)
            print(f"🌐 App will be available on your local network at:")
            print(f"   http://{local_ip}:5000")
            print("=" * 60 + "\n")
        except Exception:
            pass

    app.run(debug=True, host=args.host)

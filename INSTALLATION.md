# Sarcastic Bot Installation Guide

This guide will help you install and run the Sarcastic Bot on your computer.

## Prerequisites

- Python 3.8 or higher
- Internet connection (for OpenAI API)
- OpenAI API key (get one at https://platform.openai.com/api-keys)

## Installation Steps

### Windows Users

1. Extract the ZIP file to a folder on your computer
2. Double-click the `run_sarcastic_bot.bat` file
3. Follow the on-screen instructions
4. When prompted, edit the `.env` file to add your OpenAI API key
5. Run the batch file again to start the bot

### macOS/Linux Users

1. Extract the ZIP file to a folder on your computer
2. Open Terminal and navigate to the extracted folder
3. Make the run script executable: `chmod +x run_sarcastic_bot.sh`
4. Run the script: `./run_sarcastic_bot.sh`
5. When prompted, edit the `.env` file to add your OpenAI API key
6. Run the script again to start the bot

## Getting an OpenAI API Key

1. Create an account at https://platform.openai.com/
2. Navigate to API keys: https://platform.openai.com/api-keys
3. Create a new API key
4. Copy the key and paste it in the `.env` file

## Sharing with Others on Your Network

When the bot is running, it will display a URL like:
```
🌐 Sarcastic Bot will be available at: http://*************:8000
```

Share this URL with others on your local network, and they can access your bot from their devices.

## Troubleshooting

- **OpenAI API errors**: If you see errors about API quota or invalid API key, check your OpenAI account for billing information or try a different API key.
- **Browser issues**: If the browser doesn't open automatically, manually navigate to the URL shown in the terminal (usually http://127.0.0.1:8000).
- **Network sharing**: If others can't access your bot on the network, check your firewall settings to ensure the port (8000) is allowed.

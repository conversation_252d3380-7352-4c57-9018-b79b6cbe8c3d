[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sarcastic-bot"
version = "1.0.0"
description = "A witty, sarcastic chatbot with a beautiful UI that runs locally"
readme = "README.md"
authors = [
    {name = "Sarcastic Bot Creator", email = "<EMAIL>"}
]
license = {text = "MIT"}
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = [
    "Flask==2.3.3",
    "openai==1.3.0",
    "python-dotenv==1.0.0",
    "Werkzeug==2.3.7",
    "itsdangerous==2.1.2",
    "Jinja2==3.1.2",
    "MarkupSafe==2.1.3",
    "waitress==2.1.2",
    "feedparser==6.0.11",
    "beautifulsoup4==4.13.4",
    "requests==2.32.3",
]

[project.scripts]
sarcastic-bot = "sarcastic_bot.cli:main"

[tool.setuptools]
packages = ["sarcastic_bot"]
include-package-data = true


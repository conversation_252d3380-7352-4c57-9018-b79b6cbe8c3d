from ntscraper import <PERSON><PERSON>

def get_tweets_about(topic, max_results=5):
    """Get recent tweets about a specific topic using ntscraper"""
    try:
        print(f"Fetching tweets about: {topic}")
        scraper = Nitter()
        tweets = scraper.get_tweets(topic, number=max_results)

        if tweets:
            # Format the tweets
            formatted_tweets = []
            for tweet in tweets:
                text = tweet.get('text', 'No text available')
                formatted_tweets.append(f"- {text}")

            return "\n".join(formatted_tweets)
        else:
            print(f"No tweets found for topic: {topic}")
            return None
    except Exception as e:
        print(f"Error fetching tweets: {str(e)}")
        return None

# Test with a trending topic
topic = "Golden Temple"
tweets = get_tweets_about(topic)

if tweets:
    print("\nTweets about", topic)
    print(tweets)
else:
    print(f"No tweets found for {topic}")

# Try another topic
topic = "Elon Musk"
tweets = get_tweets_about(topic)

if tweets:
    print("\nTweets about", topic)
    print(tweets)
else:
    print(f"No tweets found for {topic}")

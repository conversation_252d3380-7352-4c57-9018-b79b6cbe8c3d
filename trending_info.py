"""
Trending Information Module for Sarcastic Bot

This module provides functions to fetch trending information from various sources:
1. Google News RSS feeds for real-time news
2. Trending topics from Google Trends
"""

import feedparser
import requests
import logging
import json
import re
from datetime import datetime
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_latest_news(query, max_results=3):
    """Get real-time news from Google News RSS feed

    Args:
        query (str): The search query
        max_results (int): Maximum number of results to return

    Returns:
        str: Formatted news results or None if no results found
    """
    try:
        logger.info(f"Fetching Google News RSS for: {query}")

        # Format the query for URL
        formatted_query = query.replace(' ', '+')
        url = f"https://news.google.com/rss/search?q={formatted_query}&hl=en-US&gl=US&ceid=US:en"

        # Parse the RSS feed
        feed = feedparser.parse(url)

        if not feed.entries:
            logger.warning(f"No news found for query: {query}")
            return None

        # Get the top results
        results = []
        for i, entry in enumerate(feed.entries[:max_results]):
            title = entry.title
            link = entry.link
            published = entry.published if hasattr(entry, 'published') else "Unknown date"
            summary = entry.summary if hasattr(entry, 'summary') else ""

            # Clean up the summary (remove HTML tags)
            if summary:
                soup = BeautifulSoup(summary, 'html.parser')
                summary = soup.get_text()

            # Format the result
            result = f"• {title}\n  Published: {published}\n  {summary}\n  Source: {link}"
            results.append(result)

            logger.info(f"Found news: {title[:50]}...")

        if not results:
            logger.warning(f"No usable news found for query: {query}")
            return None

        # Format the results with current date and time
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_results = "\n\n".join(results)

        # Create a more structured summary with date emphasis
        summary = f"REAL-TIME NEWS ABOUT {query.upper()} (as of {current_date}):\n\n{formatted_results}"

        # Log the final results
        logger.info(f"Successfully found {len(results)} news articles about {query}")
        logger.info(f"First article: {results[0][:100]}...")

        return summary

    except Exception as e:
        logger.error(f"Error fetching Google News RSS: {str(e)}")
        return None

def get_trending_topics(country_code="US", max_results=5):
    """Get trending topics from Google Trends

    Args:
        country_code (str): Country code for trends (default: US)
        max_results (int): Maximum number of results to return

    Returns:
        list: List of trending topics or empty list if failed
    """
    try:
        logger.info(f"Fetching trending topics for country: {country_code}")

        # Google Trends API endpoint for daily trends
        url = f"https://trends.google.com/trends/api/dailytrends?geo={country_code}&hl=en-US"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            logger.warning(f"Failed to fetch trending topics: {response.status_code}")
            return []

        # Google Trends API returns a JSON with a prefix that needs to be removed
        json_data = response.text[5:]  # Remove ")]}'"
        data = json.loads(json_data)

        trending_topics = []

        # Extract trending searches
        if 'default' in data and 'trendingSearchesDays' in data['default']:
            for day in data['default']['trendingSearchesDays']:
                if 'trendingSearches' in day:
                    for trend in day['trendingSearches'][:max_results]:
                        if 'title' in trend:
                            topic = trend['title']['query']
                            trending_topics.append(topic)
                            logger.info(f"Found trending topic: {topic}")

        return trending_topics[:max_results]

    except Exception as e:
        logger.error(f"Error fetching trending topics: {str(e)}")
        return []

def get_trending_news(max_topics=3, max_results_per_topic=2):
    """Get news about trending topics

    Args:
        max_topics (int): Maximum number of trending topics to fetch
        max_results_per_topic (int): Maximum number of news articles per topic

    Returns:
        str: Formatted news about trending topics or None if failed
    """
    try:
        # Get trending topics
        trending_topics = get_trending_topics(max_results=max_topics)

        if not trending_topics:
            logger.warning("No trending topics found")
            # Use fallback topics if no trending topics found
            trending_topics = ["World News", "Technology", "Sports"]

        # Get news for each trending topic
        all_news = []

        for topic in trending_topics:
            news = get_latest_news(topic, max_results=max_results_per_topic)
            if news:
                all_news.append(f"# TRENDING: {topic.upper()}\n\n{news}")

        if not all_news:
            logger.warning("No news found for any trending topics")
            return None

        # Combine all news
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        combined_news = f"TRENDING NEWS (as of {current_date}):\n\n" + "\n\n".join(all_news)

        return combined_news

    except Exception as e:
        logger.error(f"Error getting trending news: {str(e)}")
        return None

def build_personality_prompt_with_news(personality_name, topic, is_serious=False):
    """Build a prompt for a personality to react to news with context-aware sarcasm

    Args:
        personality_name (str): Name of the personality
        topic (str): Topic to get news about
        is_serious (bool): Whether the topic is serious

    Returns:
        str: Prompt with news context
    """
    # Get news about the topic
    news_snippet = get_latest_news(topic, max_results=2)

    # Log the news snippet for debugging
    logger.info(f"News snippet for {personality_name} about {topic}: {news_snippet[:200] if news_snippet else 'None'}...")

    if not news_snippet:
        # If no news found, create a generic prompt
        logger.info(f"No news found for {topic}, using generic prompt")
        news_snippet = f"No specific news found about {topic}. Please respond as {personality_name} commenting on this topic based on your general knowledge."

    # Base prompt with personality-specific language patterns
    base_prompt = f"""
You are now roleplaying as {personality_name}.

Stay fully in character — {"thoughtful and measured" if is_serious else "sarcastic, witty, and exaggerated"}, just like {personality_name} is known publicly.

Here is recent real-world news about a topic:
"{news_snippet}"
"""

    # Different instructions based on whether the topic is serious
    if is_serious:
        specific_instructions = f"""
This appears to be a serious or sensitive topic. React to this news with appropriate restraint and thoughtfulness, while still maintaining {personality_name}'s speaking style and perspective. You are not an assistant or bot — you're {personality_name} making a public statement about this serious matter.
"""
    else:
        specific_instructions = f"""
React to this news with {personality_name}'s characteristic style. Be sarcastic, witty, and exaggerated in your response, just like {personality_name} would be in real life. You are not an assistant or bot — you're {personality_name} commenting on this news.
"""

    return base_prompt + specific_instructions

# For testing
if __name__ == "__main__":
    # Test get_latest_news
    print("\nTesting get_latest_news:")
    news = get_latest_news("Elon Musk")
    if news:
        print(news[:500] + "...")

    # Test get_trending_topics
    print("\nTesting get_trending_topics:")
    topics = get_trending_topics()
    print(topics)

    # Test get_trending_news
    print("\nTesting get_trending_news:")
    trending_news = get_trending_news()
    if trending_news:
        print(trending_news[:500] + "...")

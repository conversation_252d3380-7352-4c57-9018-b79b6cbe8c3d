from setuptools import setup, find_packages

setup(
    name="sarcastic-bot",
    version="1.0.0",
    packages=find_packages(),
    include_package_data=True,
    install_requires=[
        "Flask==2.3.3",
        "openai==1.3.0",
        "python-dotenv==1.0.0",
        "Werkzeug==2.3.7",
        "itsdangerous==2.1.2",
        "Jinja2==3.1.2",
        "MarkupSafe==2.1.3",
        "waitress==2.1.2",
    ],
    entry_points={
        "console_scripts": [
            "sarcastic-bot=sarcastic_bot.cli:main",
        ],
    },
    python_requires=">=3.8",
    author="Your Name",
    author_email="<EMAIL>",
    description="A sarcastic chatbot with a beautiful UI that runs locally",
    keywords="chatbot, sarcastic, openai, flask",
    url="https://github.com/yourusername/sarcastic-bot",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)

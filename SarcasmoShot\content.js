// Content script for SarcasmoShot Chrome Extension

console.log("SarcasmoShot content script loaded");

// Listen for messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "showSarcasticModal") {
    showSarcasticModal(request.selectedText);
  }
});

// Function to create and show the sarcastic modal
function showSarcasticModal(selectedText) {
  // Remove existing modal if present
  const existingModal = document.getElementById('sarcasmo-shot-modal');
  if (existingModal) {
    existingModal.remove();
  }
  
  // Create modal HTML
  const modal = document.createElement('div');
  modal.id = 'sarcasmo-shot-modal';
  modal.innerHTML = `
    <div class="sarcasmo-shot-overlay">
      <div class="sarcasmo-shot-modal">
        <div class="sarcasmo-shot-header">
          <h3>🎭 SarcasmoShot</h3>
          <button class="sarcasmo-shot-close" id="sarcasmo-close-btn">&times;</button>
        </div>
        
        <div class="sarcasmo-shot-content">
          <div class="sarcasmo-shot-section">
            <label>Original Text:</label>
            <div class="sarcasmo-shot-original-text">${escapeHtml(selectedText)}</div>
          </div>
          
          <div class="sarcasmo-shot-section">
            <label for="sarcasm-level">Sarcasm Level:</label>
            <select id="sarcasm-level" class="sarcasmo-shot-select">
              <option value="mild">😊 Low (Gentle Wit)</option>
              <option value="medium" selected>😏 Medium (Classic Sarcasm)</option>
              <option value="extreme">😈 High (Savage Mode)</option>
            </select>
          </div>
          
          <button id="generate-sarcasm-btn" class="sarcasmo-shot-btn sarcasmo-shot-btn-primary">
            ✨ Generate Sarcasm
          </button>
          
          <div id="sarcastic-result" class="sarcasmo-shot-result" style="display: none;">
            <label>Sarcastic Version:</label>
            <div id="sarcastic-text" class="sarcasmo-shot-sarcastic-text"></div>
            <button id="copy-to-clipboard-btn" class="sarcasmo-shot-btn sarcasmo-shot-btn-secondary">
              📋 Copy to Clipboard
            </button>
          </div>
          
          <div id="loading-indicator" class="sarcasmo-shot-loading" style="display: none;">
            <div class="sarcasmo-shot-spinner"></div>
            <span>Brewing sarcasm...</span>
          </div>
          
          <div id="error-message" class="sarcasmo-shot-error" style="display: none;"></div>
        </div>
      </div>
    </div>
  `;
  
  // Add modal to page
  document.body.appendChild(modal);
  
  // Add event listeners
  setupModalEventListeners(selectedText);
}

// Function to setup event listeners for the modal
function setupModalEventListeners(selectedText) {
  // Close modal
  document.getElementById('sarcasmo-close-btn').addEventListener('click', closeModal);
  document.querySelector('.sarcasmo-shot-overlay').addEventListener('click', (e) => {
    if (e.target.classList.contains('sarcasmo-shot-overlay')) {
      closeModal();
    }
  });
  
  // Generate sarcasm
  document.getElementById('generate-sarcasm-btn').addEventListener('click', () => {
    generateSarcasm(selectedText);
  });
  
  // Copy to clipboard
  document.getElementById('copy-to-clipboard-btn').addEventListener('click', copyToClipboard);
  
  // ESC key to close
  document.addEventListener('keydown', handleEscKey);
}

// Function to generate sarcasm
async function generateSarcasm(text) {
  const loadingIndicator = document.getElementById('loading-indicator');
  const errorMessage = document.getElementById('error-message');
  const result = document.getElementById('sarcastic-result');
  const generateBtn = document.getElementById('generate-sarcasm-btn');
  const sarcasmLevel = document.getElementById('sarcasm-level').value;
  
  // Show loading, hide others
  loadingIndicator.style.display = 'flex';
  errorMessage.style.display = 'none';
  result.style.display = 'none';
  generateBtn.disabled = true;
  
  try {
    // Send message to background script
    const response = await new Promise((resolve) => {
      chrome.runtime.sendMessage({
        action: "getSarcasticText",
        text: text,
        sarcasmLevel: sarcasmLevel
      }, resolve);
    });
    
    if (response.success) {
      // Show result
      document.getElementById('sarcastic-text').textContent = response.data.response || response.data.bot_response || "No sarcastic response received";
      result.style.display = 'block';
    } else {
      throw new Error(response.error || "Unknown error occurred");
    }
  } catch (error) {
    console.error("Error generating sarcasm:", error);
    errorMessage.textContent = error.message || "Sarcasm engine overloaded. Try again.";
    errorMessage.style.display = 'block';
  } finally {
    loadingIndicator.style.display = 'none';
    generateBtn.disabled = false;
  }
}

// Function to copy text to clipboard
async function copyToClipboard() {
  const sarcasticText = document.getElementById('sarcastic-text').textContent;
  
  try {
    await navigator.clipboard.writeText(sarcasticText);
    
    // Show success feedback
    const copyBtn = document.getElementById('copy-to-clipboard-btn');
    const originalText = copyBtn.textContent;
    copyBtn.textContent = '✅ Copied!';
    copyBtn.style.backgroundColor = '#10b981';
    
    setTimeout(() => {
      copyBtn.textContent = originalText;
      copyBtn.style.backgroundColor = '';
    }, 2000);
  } catch (error) {
    console.error("Failed to copy to clipboard:", error);
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = sarcasticText;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
  }
}

// Function to close modal
function closeModal() {
  const modal = document.getElementById('sarcasmo-shot-modal');
  if (modal) {
    modal.remove();
  }
  document.removeEventListener('keydown', handleEscKey);
}

// Function to handle ESC key
function handleEscKey(e) {
  if (e.key === 'Escape') {
    closeModal();
  }
}

// Utility function to escape HTML
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

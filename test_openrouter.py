import os
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key from environment
api_key = os.getenv("OPENROUTER_API_KEY", "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")
print(f"Using API key: {api_key[:10]}...")

# Configure OpenAI with OpenRouter
openai.api_key = api_key
openai.api_base = "https://openrouter.ai/api/v1"

# Set default headers for OpenRouter
openai.api_key_path = None
openai.organization = None

# Create a simple client for API calls
client = openai

# Test API call
try:
    print("Testing API connection...")
    response = client.ChatCompletion.create(
        model="anthropic/claude-3-haiku",
        messages=[{"role": "user", "content": "Say 'API is working' in 5 words or less"}],
        max_tokens=10
    )
    
    print(f"Response: {response}")
    print(f"Message content: {response.choices[0].message.content}")
    print("API test successful!")
except Exception as e:
    print(f"API test failed: {str(e)}")

# Test with a more complex prompt
try:
    print("\nTesting with a more complex prompt...")
    response = client.ChatCompletion.create(
        model="anthropic/claude-3-haiku",
        messages=[
            {"role": "system", "content": "You are a helpful assistant that provides accurate information."},
            {"role": "user", "content": "Tell me about the latest technology trends in 2025"}
        ],
        max_tokens=100
    )
    
    print(f"Response: {response}")
    print(f"Message content: {response.choices[0].message.content}")
    print("Complex API test successful!")
except Exception as e:
    print(f"Complex API test failed: {str(e)}")

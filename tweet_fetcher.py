"""
Tweet Fetcher <PERSON> for Sarcastic Bot

This module provides functions to fetch tweets using snscrape.
It fetches tweets about specific topics and selects one randomly for the bot to comment on.
"""

import random
import logging
import subprocess
import json
import re
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_tweet(tweet_text):
    """Clean tweet text by removing URLs, mentions, and special characters"""
    # Remove URLs
    tweet_text = re.sub(r'https?://\S+', '', tweet_text)
    # Remove mentions
    tweet_text = re.sub(r'@\w+', '', tweet_text)
    # Remove hashtags (optional, you might want to keep these)
    # tweet_text = re.sub(r'#\w+', '', tweet_text)
    # Remove extra whitespace
    tweet_text = re.sub(r'\s+', ' ', tweet_text).strip()
    return tweet_text

def get_tweets_about(topic, max_results=10, days_back=7):
    """
    Get recent tweets about a specific topic using snscrape via subprocess
    
    Args:
        topic (str): The topic to search for
        max_results (int): Maximum number of tweets to fetch
        days_back (int): How many days back to search
        
    Returns:
        dict: A randomly selected tweet with its metadata or None if no tweets found
    """
    try:
        logger.info(f"Fetching tweets about: {topic}")
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        # Format dates for query
        since_date = start_date.strftime("%Y-%m-%d")
        until_date = end_date.strftime("%Y-%m-%d")
        
        # Build the snscrape command
        # Using subprocess instead of the Python API due to potential compatibility issues
        query = f"{topic} since:{since_date} until:{until_date} lang:en -filter:replies -filter:retweets"
        command = [
            "snscrape", "--jsonl", "--max-results", str(max_results),
            "twitter-search", query
        ]
        
        logger.info(f"Running command: {' '.join(command)}")
        
        # Run the command and capture output
        process = subprocess.run(command, capture_output=True, text=True)
        
        if process.returncode != 0:
            logger.error(f"snscrape command failed: {process.stderr}")
            return None
        
        # Parse the JSON lines output
        tweets = []
        for line in process.stdout.strip().split('\n'):
            if line:  # Skip empty lines
                try:
                    tweet_data = json.loads(line)
                    # Extract relevant information
                    tweet = {
                        'id': tweet_data.get('id'),
                        'date': tweet_data.get('date'),
                        'content': clean_tweet(tweet_data.get('content', '')),
                        'username': tweet_data.get('user', {}).get('username', ''),
                        'url': tweet_data.get('url', '')
                    }
                    tweets.append(tweet)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from line: {line[:100]}...")
        
        if not tweets:
            logger.warning(f"No tweets found for topic: {topic}")
            return None
        
        # Select a random tweet
        random_tweet = random.choice(tweets)
        logger.info(f"Selected random tweet: {random_tweet['content'][:100]}...")
        
        return random_tweet
        
    except Exception as e:
        logger.error(f"Error fetching tweets: {str(e)}")
        return None

def get_tweet_for_personality(personality, fallback_topic=None):
    """
    Get a tweet related to a specific personality
    
    Args:
        personality (str): The personality to get tweets about
        fallback_topic (str): Fallback topic if no tweets found for personality
        
    Returns:
        dict: A randomly selected tweet with its metadata or None if no tweets found
    """
    # Map personalities to relevant search topics
    personality_topics = {
        "trump": ["Donald Trump", "Trump presidency", "Trump policy"],
        "imran khan": ["Imran Khan", "Pakistan politics", "PTI party"],
        "modi": ["Narendra Modi", "India politics", "BJP party"],
        "hitler": ["World War II", "Nazi Germany", "Holocaust history"],
        "elon musk": ["Elon Musk", "Tesla", "SpaceX", "Twitter"]
    }
    
    # Get topics for the personality
    topics = personality_topics.get(personality.lower(), [personality])
    
    # Try each topic until we find tweets
    for topic in topics:
        tweet = get_tweets_about(topic)
        if tweet:
            return tweet
    
    # If no tweets found for any topic, try the fallback
    if fallback_topic:
        return get_tweets_about(fallback_topic)
    
    return None

# For testing
if __name__ == "__main__":
    # Test with a trending topic
    topic = "Elon Musk"
    tweet = get_tweets_about(topic)
    
    if tweet:
        print("\nTweet about", topic)
        print(f"Username: @{tweet['username']}")
        print(f"Content: {tweet['content']}")
        print(f"URL: {tweet['url']}")
    else:
        print(f"No tweets found for {topic}")
    
    # Test with a personality
    personality = "trump"
    tweet = get_tweet_for_personality(personality)
    
    if tweet:
        print(f"\nTweet for personality: {personality}")
        print(f"Username: @{tweet['username']}")
        print(f"Content: {tweet['content']}")
        print(f"URL: {tweet['url']}")
    else:
        print(f"No tweets found for personality: {personality}")

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #6c5ce7;
            --secondary-color: #a29bfe;
            --accent-color: #fd79a8;
            --background-color: #f5f6fa;
            --card-bg: #ffffff;
            --text-color: #2d3436;
            --light-text: #636e72;
            --input-bg: #f5f6fa;
            --error-color: #e74c3c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .login-container {
            width: 90%;
            max-width: 400px;
            padding: 2rem;
            background-color: var(--card-bg);
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .header p {
            color: var(--light-text);
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--text-color);
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 2px solid var(--input-bg);
            border-radius: 5px;
            background-color: var(--input-bg);
            font-size: 1rem;
            transition: border-color 0.2s;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .login-btn {
            width: 100%;
            padding: 0.8rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .login-btn:hover {
            background-color: var(--secondary-color);
        }

        .error-message {
            color: var(--error-color);
            margin-bottom: 1rem;
            padding: 0.5rem;
            background-color: rgba(231, 76, 60, 0.1);
            border-radius: 5px;
            font-size: 0.9rem;
            text-align: center;
        }

        .demo-credentials {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: var(--input-bg);
            border-radius: 5px;
        }

        .demo-credentials h3 {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            color: var(--light-text);
        }

        .demo-credentials p {
            font-size: 0.8rem;
            margin-bottom: 0.3rem;
            color: var(--text-color);
        }

        .demo-credentials code {
            background-color: var(--card-bg);
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-family: monospace;
        }

        .or-divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1.5rem 0;
            color: var(--light-text);
        }

        .or-divider::before,
        .or-divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid var(--input-bg);
        }

        .or-divider::before {
            margin-right: 0.5rem;
        }

        .or-divider::after {
            margin-left: 0.5rem;
        }

        .social-login {
            margin-bottom: 1.5rem;
        }

        .google-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 0.8rem;
            background-color: #fff;
            color: #757575;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.2s;
            text-decoration: none;
        }

        .google-btn:hover {
            background-color: #f5f5f5;
        }

        .google-btn i {
            margin-right: 0.8rem;
            color: #4285F4;
            font-size: 1.2rem;
        }

        .login-note {
            text-align: center;
            font-size: 0.8rem;
            color: var(--light-text);
            margin-top: 0.5rem;
            opacity: 0.8;
        }

        .flash-messages {
            margin-bottom: 1rem;
        }

        .flash-message {
            padding: 0.75rem 1.25rem;
            border-radius: 5px;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }

        .flash-message.error {
            background-color: rgba(231, 76, 60, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .flash-message.success {
            background-color: rgba(46, 204, 113, 0.2);
            color: #27ae60;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }

        .flash-message.info {
            background-color: rgba(52, 152, 219, 0.2);
            color: #2980b9;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        .flash-message.warning {
            background-color: rgba(241, 196, 15, 0.2);
            color: #f39c12;
            border: 1px solid rgba(241, 196, 15, 0.3);
        }

        .error-message {
            background-color: rgba(231, 76, 60, 0.2);
            color: var(--error-color);
            border: 1px solid rgba(231, 76, 60, 0.3);
            padding: 0.75rem 1.25rem;
            border-radius: 5px;
            margin-bottom: 0.75rem;
        }

        .quick-login-form {
            margin-top: 1rem;
        }

        .quick-login-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 0.6rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            width: 100%;
            transition: background-color 0.2s;
        }

        .quick-login-btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="header">
            <h1>Sarcastic Bot</h1>
            <p>Please login to continue the sarcasm</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                <div class="flash-message {{ category }}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}
        {% endwith %}

        {% if error %}
        <div class="error-message">
            {{ error }}
        </div>
        {% endif %}

        <div class="social-login">
            <a href="{{ url_for('google_login') }}" class="google-btn">
                <i class="fab fa-google"></i>
                Sign in with Google
            </a>
            <p class="login-note">Use your Google account for secure login</p>
        </div>

        <div class="or-divider">OR</div>

        <form method="POST" action="{{ url_for('login') }}">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required placeholder="Enter your email">
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required placeholder="Enter your password">
            </div>
            <button type="submit" class="login-btn">Login with Email</button>
        </form>

        <div class="demo-credentials">
            <h3>Demo Credentials:</h3>
            <p>Email: <code><EMAIL></code> Password: <code>test123</code></p>
            <p>Email: <code><EMAIL></code> Password: <code>admin123</code></p>
            <p>Email: <code><EMAIL></code> Password: <code>password123</code></p>

            <form method="POST" action="{{ url_for('login') }}" class="quick-login-form">
                <input type="hidden" name="email" value="<EMAIL>">
                <input type="hidden" name="password" value="password123">
                <button type="submit" class="quick-login-btn">Quick <NAME_EMAIL></button>
            </form>
        </div>
    </div>
</body>
</html>

import os
import json
from flask import Flask, render_template, request, jsonify
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Initialize OpenAI with OpenRouter API key
try:
    # Get API key from environment
    api_key = os.getenv("OPENROUTER_API_KEY")
    print(f"Using API key: {api_key[:10]}...")

    # Configure OpenAI with OpenRouter
    openai.api_key = api_key
    openai.api_base = "https://openrouter.ai/api/v1"
    
    # Test API call
    print("Testing API connection...")
    response = openai.ChatCompletion.create(
        model="anthropic/claude-3-haiku",
        messages=[{"role": "user", "content": "Say 'API is working' in 5 words or less"}],
        max_tokens=10
    )
    
    print(f"Response: {response}")
    print(f"Message content: {response.choices[0].message.content}")
    print("API test successful!")
except Exception as e:
    print(f"API test failed: {str(e)}")

@app.route('/')
def index():
    return render_template('simple.html')

@app.route('/chat', methods=['POST'])
def chat():
    user_message = request.form.get('message', '')
    
    try:
        # Call OpenRouter API
        response = openai.ChatCompletion.create(
            model="anthropic/claude-3-haiku",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": user_message}
            ],
            max_tokens=100
        )
        
        bot_response = response.choices[0].message.content
        print(f"API response: {bot_response}")
        
        return jsonify({
            'bot_response': bot_response
        })
    except Exception as e:
        print(f"Error: {str(e)}")
        return jsonify({
            'bot_response': f"Error: {str(e)}"
        })

if __name__ == '__main__':
    app.run(debug=True, port=5001)

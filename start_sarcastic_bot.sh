#!/bin/bash

echo "Starting Sarcastic Bot..."
echo

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies if needed
echo "Checking dependencies..."
pip install -r requirements.txt > /dev/null

# Run the application in production mode
echo
echo "Starting Sarcastic Bot in production mode..."
python run_local.py

# Deactivate virtual environment on exit
deactivate
